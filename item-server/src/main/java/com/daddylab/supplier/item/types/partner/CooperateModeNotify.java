package com.daddylab.supplier.item.types.partner;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/2
 */
@Data
public class CooperateModeNotify {
    String itemNo;
    /**
     * @see com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysCooperatorEnum
     */
    List<Integer> cooperator;
    /**
     * @see com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysBusinessTypeEnum
     */
    @JsonProperty("business_type")
    List<Integer> businessType;
}

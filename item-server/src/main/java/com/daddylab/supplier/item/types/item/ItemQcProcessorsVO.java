package com.daddylab.supplier.item.types.item;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/21
 */
public class ItemQcProcessorsVO {

    private String qcIds;

    public List<Long> getQcIds() {
        return Optional.ofNullable(qcIds)
                       .filter(StringUtil::isNotBlank)
                       .map(v -> StringUtil.splitTrim(qcIds, ",")
                                           .stream()
                                           .map(Long::parseLong)
                                           .collect(Collectors.toList()))
                       .orElseGet(Collections::emptyList);
    }

    public void setQcIds(String qcIds) {
        this.qcIds = qcIds;
    }
}

package com.daddylab.supplier.item.domain.wdt.service;

import com.daddylab.mall.wdtsdk.WdtErpException;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/1/6
 */
public interface PlatformItemFetchService {
    /**
     *
     * @param startTime
     * @param endTime
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws WdtErpException
     */
    int fetch(LocalDateTime startTime, LocalDateTime endTime, int pageIndex,
              int pageSize) throws WdtErpException;

    int count(LocalDateTime startTime, LocalDateTime endTime) throws WdtErpException;

    /**
     *
     * @param startTime
     * @param endTime
     * @param pageIndex
     * @param pageSize
     * @param goodsId
     * @param specId
     * @param shopNo
     * @return
     * @throws WdtErpException
     */
    int fetch(LocalDateTime startTime, LocalDateTime endTime, int pageIndex, int pageSize
            , String goodsId, String specId, String shopNo) throws WdtErpException;
}

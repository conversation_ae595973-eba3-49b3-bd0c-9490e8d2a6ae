package com.daddylab.supplier.item.domain.wdt.service;

import com.alibaba.cola.exception.BizException;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.sales.StockSync;
import com.daddylab.mall.wdtsdk.api.sales.dto.CalcStockResponse;
import com.daddylab.mall.wdtsdk.api.sales.dto.GetSelfWaitSyncIdListOpenResponse;
import com.daddylab.mall.wdtsdk.api.sales.dto.StockSyncInfo;
import com.daddylab.supplier.item.domain.alert.IAlert;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.function.Consumer;

/**
 * 旺店通同步平台库存
 * <AUTHOR>
 */
@Slf4j
@Service
@Deprecated
public class PlatformStockSyncService {
    @Inject
    WdtGateway wdtGateway;
    @Inject
    IAlert alert;

    void stockSyncConsume(Consumer<CalcStockResponse> consumer) throws WdtErpException {
        final StockSync stockSyncAPI = wdtGateway.stockSyncAPI();

        int position = 0;
        int continuousFailure = 0;
        final int continuousFailureThreshold = 3;
        while(true) {
            final GetSelfWaitSyncIdListOpenResponse response = stockSyncAPI.getSelfWaitSyncIdListOpen(100, position);
            if (response.getPosition().compareTo(0) <= 0) {
                break;
            }
            for (Long id : response.getIdList()) {
                CalcStockResponse calcStockResponse = null;
                try {
                    calcStockResponse = stockSyncAPI.calcStock(id, false);
                    consumer.accept(calcStockResponse);

                    final StockSyncInfo stockSyncInfo = buildStockSyncInfo(calcStockResponse);
                    stockSyncInfo.setSynSuccess(true);
                    stockSyncInfo.setSynResult("同步成功");
                    stockSyncAPI.syncSuccess(id, stockSyncInfo);
                } catch (Exception e) {
                    continuousFailure++;
                    log.error("旺店通库存同步处理库存计算失败", e);

                    if (calcStockResponse != null) {
                        final StockSyncInfo stockSyncInfo = buildStockSyncInfo(calcStockResponse);
                        stockSyncInfo.setSynSuccess(false);
                        stockSyncInfo.setSynResult(e instanceof BizException ? e.getMessage() : "未知异常");
                        stockSyncAPI.syncSuccess(id, stockSyncInfo);
                    }
                }

                //连续失败超过阈值直接停止
                if (continuousFailure > continuousFailureThreshold) {
                    alert.text(MessageRobotCode.GLOBAL, "旺店通库存同步连续多次调用失败");
                    break;
                }
            }
            position = response.getPosition();
        }
    }

    private StockSyncInfo buildStockSyncInfo(CalcStockResponse calcStockResponse) {
        final StockSyncInfo info = new StockSyncInfo();
        info.setSynStock(calcStockResponse.getSynStock());
        info.setStockChangeCount(calcStockResponse.getStockChangeCount());
        info.setStockSynRuleId(calcStockResponse.getStockSynRuleId());
        info.setStockSynRuleNo(calcStockResponse.getStockSynRuleNo());
        info.setStockSynOther("");
        info.setStockSynWarehouses(calcStockResponse.getStockSynWarehouses());
        info.setStockSynMask(calcStockResponse.getStockSynMask());
        info.setStockSynPercent(calcStockResponse.getStockSynPercent());
        info.setStockSynPlus(calcStockResponse.getStockSynPlus());
        info.setStockSynMin(calcStockResponse.getStockSynMin());
        info.setStockSynMax(calcStockResponse.getStockSynMax());
        info.setAutoListing(calcStockResponse.getIsAutoListing() > 0);
        info.setAutoDelisting(calcStockResponse.getIsAutoDelisting() > 0);
        info.setManual(false);
        return info;
    }
}

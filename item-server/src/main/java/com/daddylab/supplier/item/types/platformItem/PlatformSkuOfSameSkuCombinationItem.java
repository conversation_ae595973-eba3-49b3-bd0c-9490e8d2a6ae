package com.daddylab.supplier.item.types.platformItem;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlatformSkuOfSameSkuCombinationItem {
    private Platform platform;
    private String shopNo;
    private Long combinationId;
    private String combinationCode;
    private Long composeSkuId;
    private String skuCode;
    private String itemCode;
    private Long skuId;
    private Long itemId;
    private Integer num;
    private Long combinationId2;
    private String combinationCode2;
    private Long composeSkuId2;
    private String skuCode2;
    private String itemCode2;
    private Long skuId2;
    private Long itemId2;
    private Integer num2;
    private Long platformItemSkuId;
    private String outerSkuId;
    private String outerItemId;
    private String outerSkuCode;
    private String outerItemCode;
}

package com.daddylab.supplier.item.types.itemBatchProc;

import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/11/13
 */
@Data
public class ModifyWarehouseCmd {
    @ApiModelProperty(value = "商品查询条件", required = true)
    @NotNull
    @Valid
    ItemPageQuery query;

    @ApiModelProperty(value = "仓库编号", required = true)
    @NotBlank
    String warehouseNo;
}

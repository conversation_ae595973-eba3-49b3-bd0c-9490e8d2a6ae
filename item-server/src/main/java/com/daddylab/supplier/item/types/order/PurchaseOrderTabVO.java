package com.daddylab.supplier.item.types.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2022/4/19 10:28 上午
 */
@Data
@ApiModel("采购订单页面商品明细tab")
public class PurchaseOrderTabVO {

    @ApiModelProperty("订单编号（旺）")
    private String wdtTradeId;

    @ApiModelProperty("店铺")
    private String shop;

    @ApiModelProperty("订单状态。4 线下退款 5已取消 6 待确认订单, 导入放入这个状态  待转预订单(待审核) 7待确认订单,导入时先放到这个状态（此状态不占用库存,可删除,离开这个状态就不能删除了) 10待付款 12待尾款 15等未付 16延时审核 19预订单前处理 20前处理(赠品，合并，拆分) " +
            "21委外前处理 23 异常预订单 24 换货预订单 25待处理预订单 26 待激活预订单 27待分配预订单 30待客审 35待财审 55已审核 95已发货 96 成本确认（待录入计划成本，订单结算时有货品无计划成本） 101 已过账 110已完成")
    private Integer tradeStatus;

    @ApiModelProperty("下单时间")
    private Long tradeTime;

    @ApiModelProperty("付款时间")
    private Long payTime;

    @ApiModelProperty("发货时间")
    private Long consignTime;

    @ApiModelProperty("仓库")
    private String warehouse;

    @ApiModelProperty("wdt平台id")
    private Integer wdtPlatformId;

    @ApiModelProperty("平台名称")
    private String platform;

    @ApiModelProperty("货品名称")
    private String itemName;

    @ApiModelProperty("规格名称")
    private String specifications;

    @ApiModelProperty("商品SKU")
    private String skuCode;

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("关联类型。1:生成采购单。2.月退货。3.次月退货，4.生成采购订单")
    private Integer relationType;

}

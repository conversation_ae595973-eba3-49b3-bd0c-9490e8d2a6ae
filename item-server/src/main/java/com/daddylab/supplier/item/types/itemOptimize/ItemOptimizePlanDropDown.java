package com.daddylab.supplier.item.types.itemOptimize;



import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Data
public class ItemOptimizePlanDropDown {

    /**
     * ID
     */
	@ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 编号
     */
	@ApiModelProperty(value = "编号")
    private String planNo;

    /**
     * 名称
     */
	@ApiModelProperty(value = "名称")
    private String planName;

}

package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.platformItem.model.PlatformItemSkuStat;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PlatformSkuRefQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.types.platformItem.ComposeSkuPlatformItem;
import com.daddylab.supplier.item.types.platformItem.PlatformSkuOfSameSkuCombinationItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 平台商品（投放到其他平台的商品）SKU维度 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Repository
public interface PlatformItemSkuMapper extends DaddyBaseMapper<PlatformItemSku> {
    /**
     * 统计平台商品SKU
     *
     * @param platformItemId 平台商品ID
     * @return PlatformItemSkuStat
     */
    PlatformItemSkuStat statPlatformItemSku(@Param("platformItemId") long platformItemId);


    /**
     * 查询长时间未更新的SKU
     *
     * @param shopNo     店铺编号
     * @param updateTime 更新时间小于此时间的视为无效商品
     * @param timeOffset timeOffset
     * @param limit      LIMIT
     * @return
     */
    List<PlatformItemSku> selectInvalidSkuList(@Param("shopNo") String shopNo, @Param("updateTime") Long updateTime, @Param("timeOffset") Long timeOffset, @Param("limit") int limit);

    /**
     * 更新平台商品统计数据（包括调整平台商品状态、删除标记）
     *
     * @param platformItemIds 平台商品ID（内部ID）
     * @return 更新记录数量
     */
    int updatePlatformItemStats(@Param("platformItemIds") Collection<Long> platformItemIds);

    /**
     * 组合装套内单品关联的平台商品SKU
     *
     * @param query 查询参数模型
     */
    List<ComposeSkuPlatformItem> platformSkuQueryOfComposeSku(@Param("query") PlatformSkuRefQuery query);

    /**
     * 查询与指定组合装包含了相同单品的其他组合装关联的平台商品
     * @param query 查询参数模型
     */
    List<PlatformSkuOfSameSkuCombinationItem> platformSkuQueryOfSameSkuCombinationItem(@Param("query") PlatformSkuRefQuery query);


    /**
     * 查询平台商品的外部编码
     * @param query 查询条件
     * @return 外部编码列表
     */
    List<String> listOuterSkuCode(@Param("query") ListOuterSkuCodeQuery query);
}

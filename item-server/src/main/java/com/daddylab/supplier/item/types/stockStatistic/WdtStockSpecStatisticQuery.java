package com.daddylab.supplier.item.types.stockStatistic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@Data
public class WdtStockSpecStatisticQuery {

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("商品SPU")
    private String skuCode;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("仓库编号")
    private List<String> warehouseNos;
}

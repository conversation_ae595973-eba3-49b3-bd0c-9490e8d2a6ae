package com.daddylab.supplier.item.types.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/16
 */
@Data
@ApiModel("订单详情退换明细")
@Deprecated
public class OrderRefundDetail {

    /**
     * 退款单明细ID
     **/
    @ApiModelProperty("退款单明细ID")
    Long id;

    /**
     * 退款单号
     **/
    @ApiModelProperty("退款单号")
    String refundOrderNo;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    Long createTime;

    /**
     * 售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款
     **/
    @ApiModelProperty("售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款")
    Integer type;


    /**
     * 原始退换单号
     **/
    @ApiModelProperty("原始退换单号")
    public List<String> getSrcRefundOrderNo() {
        if (CollUtil.isNotEmpty(items)) {
            return items.stream().map(OrderRefundItem::getSrcRefundOrderNos)
                    .map(nos -> StrUtil.splitTrim(nos, ','))
                    .reduce((a, b) -> {
                        a.addAll(b);
                        return a;
                    }).orElse(Collections.emptyList());
        }
        return Collections.emptyList();
    }

    /**
     * 退换商品明细
     */
    @ApiModelProperty("退换商品明细")
    List<OrderRefundItem> items;
}

package com.daddylab.supplier.item.types.itemBatchProc;

import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */
@Data
@JsonInclude
public class ModifyProviderCmd {
    @ApiModelProperty(value = "商品查询条件", required = true)
    @NotNull
    @Valid
    private ItemPageQuery query;

    @ApiModelProperty(value = "供应商ID", required = true)
    @Positive
    private Long providerId;
}

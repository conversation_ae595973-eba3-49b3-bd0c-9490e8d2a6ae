package com.daddylab.supplier.item.types.handingsheet;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@Data
public class HandingSheetItemSkuBringOutVO {
    @ApiModelProperty("SKU ID")
    private Long skuId;

    @ApiModelProperty("SKU编码")
    private String skuCode;

    @ApiModelProperty("规格名称")
    private String specifications;

    @ApiModelProperty("所属平台")
    private List<Integer> platforms;

    @ApiModelProperty("商品类目ID（后端）")
    private Long categoryId;

    @ApiModelProperty("商品类目（后端）")
    private String categoryPath;
}

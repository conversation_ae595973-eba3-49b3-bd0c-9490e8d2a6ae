package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.CombinationItemNameVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.CombinationItemWithPriceVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.CombinationPageVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuShortVO;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposeSkuBaseSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuWithItemPriceSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuWithSkuPriceSheet;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/11 4:07 下午
 * @description
 */
@Mapper
public interface CombinationItemTransMapper {
    CombinationItemTransMapper INSTANCE = Mappers.getMapper(CombinationItemTransMapper.class);

//    CombinationItem cmdToDb(CombinationItemSaveCmd cmd);

    @Mapping(target = "id", ignore = true)
    ComposeSku skuCmdToDb(ComposeSkuCmd cmd);

//    @Mapping(target = "id", ignore = true)
//    ComposeSku skuToSku(ComposeSku cmd);

//    List<ComposeSku> skuCmdToDbs(List<ComposeSkuCmd> cmd);

    @Mappings({
            @Mapping(target = "createdName", ignore = true),
            @Mapping(target = "updatedName", ignore = true),
            @Mapping(target = "procurementPrices", ignore = true),
            @Mapping(target = "salesPrices", ignore = true)
    })
    CombinationItemWithPriceVO dbToVoWithPrice(CombinationItem combinationItem);

    CombinationItemNameVO dbToNameVo(CombinationItem item);

    List<CombinationItemNameVO> dbToNameVos(List<CombinationItem> item);

    @Mappings({
            @Mapping(target = "createName", ignore = true),
            @Mapping(target = "skuShortList", ignore = true)
    })
    CombinationPageVO doToPageVo(CombinationDO combinationDO);

//    List<CombinationPageVO> doToPageVos(List<CombinationDO> combinationDO);

    ComposeSkuShortVO skuDbToShortVo(ComposeSku composeSku);

    List<ComposeSkuShortVO> skuDbToShortVos(List<ComposeSku> composeSku);

//    @Mappings({
//            @Mapping(target = "id", ignore = true),
//            @Mapping(target = "combinationId", ignore = true)
//    })
//    ComposeSku skuDoToSkuDb(ComposeSkuDO composeSkuDO);


//    ----------------- export -------------------

    ComposeSkuBaseSheet allSheetToBaseSheet(ComposerSkuAllSheet skuAllSheet);

    ComposerSkuWithItemPriceSheet allSheetToItemPriceSheet(ComposerSkuAllSheet skuAllSheet);

    ComposerSkuWithSkuPriceSheet allSheetToSkuPriceSheet(ComposerSkuAllSheet skuAllSheet);

}

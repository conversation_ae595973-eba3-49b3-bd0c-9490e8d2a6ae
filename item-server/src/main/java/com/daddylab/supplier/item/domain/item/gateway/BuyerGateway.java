package com.daddylab.supplier.item.domain.item.gateway;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 12:22 下午
 * @description
 */
public interface BuyerGateway {

    Optional<Buyer> buyerExist(Long userId);

    Long saveOrUpdateBuyer(Long userId, String userName);

    String getKingDeeId(Long userId);

    String getKingDeeAccountByItemId(Long itemId);

    List<String> getNameList();

    Optional<Buyer> buyer(Long id);

    Buyer getByItemId(Long itemId);

    Optional<Buyer> addBuyerByUid(Long uid);


}

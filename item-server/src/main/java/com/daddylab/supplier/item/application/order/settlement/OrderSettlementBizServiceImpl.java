package com.daddylab.supplier.item.application.order.settlement;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.FormulaData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageQuery;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageVO;
import com.daddylab.supplier.item.application.order.settlement.dto.*;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.application.payment.dto.PaymentDetailAmountVO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchaseBillRow;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ExternalShopWdtNo;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuNameDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.javers.core.Changes;
import org.javers.core.diff.Diff;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.convertInt;
import static java.lang.Long.parseLong;

/**
 * <AUTHOR> up
 * @date 2023年08月07日 11:25 AM
 */
@Service
@Slf4j
public class OrderSettlementBizServiceImpl implements OrderSettlementBizService {


    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    OrderSettlementFormMapper orderSettlementFormMapper;

    @Resource
    OrderSettlementDao orderSettlementDao;

    @Resource
    IWarehouseService iWarehouseService;

    @Resource
    PurchaseBillService purchaseBillService;

    @Resource
    IPurchaseOrderService iPurchaseOrderService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    OrderSettlementDetailMapper orderSettlementDetailMapper;

    @Resource
    RedissonClient redissonClient;

    @Resource
    FileGateway fileGateway;

    @Resource
    IExportTaskService iExportTaskService;

    @Resource
    IAdditionalService iAdditionalService;

    @Resource
    ItemSkuMapper itemSkuMapper;

    @Resource
    IProviderService iProviderService;

    @Resource
    WdtRefundOrderMapper wdtRefundOrderMapper;

    @Resource
    AfterSalesRegisterBizService afterSalesRegisterBizService;

    @Resource
    @Qualifier("singleExecutor")
    ExecutorService singleExecutor;

    @Resource
    IPaymentApplyOrderService iPaymentApplyOrderService;

    @Resource
    OssGateway ossGateway;

    @Resource
    IFileService iFileService;

    @Resource
    IPaymentApplyOrderDetailService ipaymentApplyOrderDetailService;

    @Resource
    PaymentApplyOrderDetailMapper paymentApplyOrderDetailMapper;

    @Autowired
    PaymentOrderBizService paymentOrderBizService;

    @Resource
    ITmpAfterSalesSupplementService iTmpAfterSalesSupplementService;

    @Resource
    ExternalShopWdtNo externalShopWdtNo;

    private final static String SETTLEMENT_EXCEL_NAME = "工厂采购订货明细单导出列表模板经典版";
    private final static String SETTLEMENT_ALL_NAME = "工厂采购订货明细单导出模板经典版";

    private final static String LOCK_BILL = "ORDER_SETTLEMENT_BILL";

    private final static String NO_PREFIX = "GCJS";

    private static final List<String> STATIC_FLAGS = new LinkedList<>();

    static {
        STATIC_FLAGS.add("小计");
        STATIC_FLAGS.add("运费");
        STATIC_FLAGS.add("售后");
        STATIC_FLAGS.add("其他");
        STATIC_FLAGS.add("合计");
    }

    private final FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();

    WriteSheet afterSalesSheet = EasyExcel.writerSheet("客服售后明细").head(AfterSalesRegisterPageVO.class).build();
    WriteSheet refundDetailSheet = EasyExcel.writerSheet("退货明细").head(RefundManageExportSheet.class).build();
    WriteSheet settlementSheet = EasyExcel.writerSheet("结算单").head(ExportSettlementOrderDo.class)
            .build();

    private static final BigDecimal EXPORT_FILE_FINISHED_PROGRESS = new BigDecimal("0.9");
    @Autowired
    private ProviderGateway providerGateway;

    @Autowired
    IBizLevelDivisionService iBizLevelDivisionService;


    @Override
    public PageResponse<SysBillPageVo> sysBillPageQuery(SysBillPageQuery query) {
        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        List<String> warehouseNoList;
        if (CollUtil.isNotEmpty(query.getOrderPersonnelIds())) {
            warehouseNoList = iWarehouseService.getWarehouseNoByOrderPersonnel(query.getOrderPersonnelIds());
            // 订单员存在，但是此订单元对应的仓库为空，直接返回空列表
            if (CollUtil.isEmpty(warehouseNoList)) {
                return PageResponse.of(new LinkedList<>(), 0, query.getPageSize(), query.getPageIndex());
            }
            // 订单员对应的仓库编号不为空，且仓库条件存在，取两者交集。
            if (CollUtil.isNotEmpty(query.getWarehouseNos())) {
                Collection<String> intersectionWarehouseNos = CollectionUtil.intersection(warehouseNoList, query.getWarehouseNos());
                if (CollUtil.isEmpty(intersectionWarehouseNos)) {
                    return PageResponse.of(new LinkedList<>(), 0, query.getPageSize(), query.getPageIndex());
                }
                warehouseNoList = (List<String>) intersectionWarehouseNos;
            }
        } else {
            if (Objects.nonNull(query.getWarehouseNos())) {
                warehouseNoList = new ArrayList<>(query.getWarehouseNos());
            } else {
                warehouseNoList = new ArrayList<>();
            }
        }
        query.setQueryWarehouseNos(warehouseNoList);
        query.setStatusVal(Objects.nonNull(query.getStatus()) ? query.getStatus().getValue() : null);
        PageInfo<OrderSettlementForm> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> orderSettlementFormMapper.selectBill(query));
        List<OrderSettlementForm> result = pageInfo.getList();
        if (CollUtil.isEmpty(result)) {
            return PageResponse.of(new LinkedList<>(), (int) pageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
        }

        List<String> warehouseNos = result.stream().map(OrderSettlementForm::getWarehouseNo).collect(Collectors.toList());
        Map<String, String> warehouseMap = iWarehouseService.getNameAndNo(warehouseNos);
        Map<String, List<String>> orderPersonnelNickName = iWarehouseService.getOrderPersonnelNickName(warehouseNos);
        Set<String> purchaseOrderNos = result.stream().map(OrderSettlementForm::getPurchaseOrderNo).collect(Collectors.toSet());
        Map<String, Integer> blMap = iPurchaseOrderService.lambdaQuery().in(PurchaseOrder::getNo, purchaseOrderNos).list()
                .stream().collect(Collectors.toMap(PurchaseOrder::getNo, PurchaseOrder::getBusinessLine, (a, b) -> a));
        List<SysBillPageVo> collect = result.stream().map(val -> {
            SysBillPageVo vo = new SysBillPageVo();
            vo.setBillId(val.getId());
            vo.setCycleDesc(DateUtil.parseTimeStamp(val.getSettlementStartDate(), DatePattern.NORM_MONTH_PATTERN));
            vo.setWarehouse(warehouseMap.getOrDefault(val.getWarehouseNo(), ""));
            vo.setTemporaryQuantity(val.getTemporaryQuantity());
            vo.setSettlementQuantity(val.getSettlementQuantity());
            vo.setStatus(val.getStatus());
            vo.setPurchaseOrderNo(val.getPurchaseOrderNo());
            List<String> names = orderPersonnelNickName.get(val.getWarehouseNo());
            vo.setOrderPersonnelName(CollUtil.isEmpty(names) ? "" : CollUtil.join(names, ","));
            vo.setBusinessLine(blMap.getOrDefault(vo.getPurchaseOrderNo(), 0));
            return vo;
        }).collect(Collectors.toList());
        return PageResponse.of(collect, (int) pageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    @Override
    public com.alibaba.cola.dto.Response sysBillBatchSettlement(SysBillPageQuery query) {
        Assert.isTrue(CollUtil.isNotEmpty(query.getSelectBillIds()), "批量结算id列表不得为空");
        List<OrderSettlementForm> forms = iOrderSettlementFormService.lambdaQuery()
                .in(OrderSettlementForm::getId, query.getSelectBillIds())
                .list();

        int errorCount = 0;
        for (OrderSettlementForm form : forms) {
            try {
                SettlementSaveCmd cmd = new SettlementSaveCmd();
                cmd.setIds(Collections.singletonList(form.getId()));
                cmd.setDetailList(getDetails(form.getId()));
                cmd.setDetailStaticDo(getDetailStaticDo(form.getId()));
                cmd.setPurProviderId(form.getPProviderId());
                cmd.setSettProviderId(form.getSProviderId());
                cmd.setIsAdd(true);
                save(cmd);
            } catch (Exception e) {
                log.error("batchSettlement 自动结算异常。formId:{}", form.getId(), e);
                errorCount++;
            }
        }
        log.info("sysBillBatchSettlement all finished.errorCount:{}", errorCount);

        if (errorCount != 0) {
            return com.alibaba.cola.dto.Response.buildFailure(ErrorCode.API_REQ_ERROR.getCode(), "存在处理失败数据");
        } else {
            return com.alibaba.cola.dto.Response.buildSuccess();
        }

    }

    private DetailStaticDo getDetailStaticDo(Long formId) {
        FormDetailCmd cmd = new FormDetailCmd();
        cmd.setIds(Collections.singletonList(formId));
        cmd.setIsAdd(true);
        FormDetailVo formDetailVo = viewFormInfo(cmd);
        return formDetailVo.getStaticDo();
    }

    private List<DetailExcelEditCmd> getDetails(Long formId) {
        List<DetailExcelEditCmd> resList = new LinkedList<>();
        List<OrderSettlementDetail> details = iOrderSettlementDetailService.lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, formId).list();
        for (OrderSettlementDetail detail : details) {
            DetailExcelEditCmd cmd = new DetailExcelEditCmd();
            cmd.setSkuCode(detail.getSkuCode());
            cmd.setTime(detail.getSettlementStartDate());
            cmd.setDeliverQuantity(detail.getDeliverQuantity());
            cmd.setTemporaryPrice(detail.getTemporaryPrice());
            cmd.setTemporaryQuantity(detail.getTemporaryQuantity());
            cmd.setCurrentMonthRefundQuantity(detail.getCurrentMonthRefundQuantity());
            cmd.setCrossMonthRefundQuantity(detail.getCrossMonthRefundQuantity());
            cmd.setSettlementPrice(detail.getSettlementPrice());
            cmd.setSettlementQuantity(detail.getSettlementQuantity());
            cmd.setSettlementAmount(detail.getSettlementAmount());
            cmd.setAfterSalesCost(detail.getAfterSalesCost());
            cmd.setFinalAmount(detail.getFinalAmount());
            cmd.setRemark(detail.getRemark());
            cmd.setSource(detail.getSource());
            resList.add(cmd);
        }
        return resList;
    }

    private void getExportBillParam(Set<String> warehouseNos, Set<Long> durations,
                                    Set<String> operateTimes, Set<RefundMangerExportCmd> refundMangerExportCmdList,
                                    ExportSysBillCmd cmd) {

        List<OrderSettlementForm> orderSettlementForms;
        // 勾选了制定账单，无视其他筛选条件，直接根据id进行匹配
        if (CollUtil.isNotEmpty(cmd.getIds())) {
            List<Long> ids = cmd.getIds();
            orderSettlementForms = iOrderSettlementFormService.lambdaQuery()
                    .in(OrderSettlementForm::getId, ids).list();
        } else {
            LambdaQueryChainWrapper<OrderSettlementForm> wrapper = iOrderSettlementFormService.lambdaQuery()
                    .in(CollUtil.isNotEmpty(cmd.getTimes()), OrderSettlementForm::getSettlementStartDate, cmd.getTimes())
                    .eq(Objects.nonNull(cmd.getStatus()), OrderSettlementForm::getStatus, cmd.getStatus())
                    .in(CollUtil.isNotEmpty(cmd.getWarehouseNos()), OrderSettlementForm::getWarehouseNo, cmd.getWarehouseNos());
            if (CollUtil.isNotEmpty(cmd.getOrderPersonnelIds())) {
                List<String> reqNos = iWarehouseService.getWarehouseNoByOrderPersonnel(cmd.getOrderPersonnelIds());
                wrapper.in(OrderSettlementForm::getWarehouseNo, reqNos);
            }
            orderSettlementForms = wrapper.list();
        }

        // 增加导出数量限制
        Assert.isTrue(orderSettlementForms.size() <= 5, "导出数量超出限制，一次最多导出5条系统单据。请分批导出");

        orderSettlementForms.forEach(val -> {
            Long settlementStartDate = val.getSettlementStartDate();
            String time = DateUtil.format(settlementStartDate, DatePattern.SIMPLE_MONTH_PATTERN);
            operateTimes.add(time);
            durations.add(settlementStartDate);
            if (StringUtils.hasText(val.getWarehouseNo())) {
                warehouseNos.addAll(Arrays.asList(val.getWarehouseNo().split(",")));
            }
        });
        Set<Long> formIds = orderSettlementForms.stream().map(OrderSettlementForm::getId).collect(Collectors.toSet());
        List<RefundMangerExportCmd> cmdList = iOrderSettlementDetailService.lambdaQuery()
                .in(OrderSettlementDetail::getFormId, formIds)
                .list().stream().map(this::convertRefundMangerExportCmd).distinct().collect(Collectors.toList());
        refundMangerExportCmdList.addAll(cmdList);
    }

    @Override
    public void exportBillData(ExportSysBillCmd cmd) {
        // 处理导出数据的筛选条件
        Set<String> warehouseNoContainer = new HashSet<>();
        Set<String> operateTimeContainer = new HashSet<>();
        Set<Long> durationsContainer = new HashSet<>();
        Set<RefundMangerExportCmd> refundContainer = new HashSet<>();
        getExportBillParam(warehouseNoContainer, durationsContainer, operateTimeContainer, refundContainer, cmd);
        log.info("exportBillData param. warehouseNoContainer:{},operateTimeContainer{},durationsContainer:{},refundContainer:{}",
                JsonUtil.toJson(warehouseNoContainer), JsonUtil.toJson(operateTimeContainer),
                JsonUtil.toJson(durationsContainer), JsonUtil.toJson(refundContainer));

        ExportTask exportTask = ExportTask.initTask("采购结算系统明细", ExportTaskType.SETTLEMENT_BILL);
        iExportTaskService.save(exportTask);

        singleExecutor.submit(() -> {
            File dataFile;
            ExcelWriter excelWriter;
            dataFile = new File("采购结算系统明细" + RandomUtil.randomString(6) + ".xlsx");
            excelWriter = EasyExcel.write(dataFile).build();
            // 发货明细，系统导出无视sku纬度的区分
            boolean b1 = purchaseBillService.writeStockOutData(operateTimeContainer, null, warehouseNoContainer, excelWriter);
            log.info("采购结算系统明细-发货明细写入完毕.result:{}", b1);
            // 退货明细
            boolean b2 = exportRefundMange0(new ArrayList<>(refundContainer), new ArrayList<>(warehouseNoContainer), excelWriter);
            log.info("采购结算系统明细-退货明细写入完毕.result:{}", b2);
            // 客服售后明细。
            boolean b3 = afterSalesDetail0(new ArrayList<>(warehouseNoContainer), new ArrayList<>(durationsContainer), excelWriter);
            log.info("采购结算系统明细-客服售后明细写入完毕.result:{}", b3);
            try {
                if (b1 && b2 && b3) {
                    excelWriter.finish();
                    UploadFileAction action = UploadFileAction.ofFile(dataFile);
                    String url = fileGateway.uploadFile(action).getUrl();
                    exportTask.setDownloadUrl(url);
                    exportTask.setStatus(ExportTaskStatus.SUCCESS);
                    iExportTaskService.updateById(exportTask);
                } else {
                    exportTask.setStatus(ExportTaskStatus.FAIL);
                    exportTask.setError("导出失败 ");
                    iExportTaskService.updateById(exportTask);
                }
            } catch (Exception e) {
                log.error("采购结算系统明细导出异常", e);
                exportTask.setError("导出异常。" + e.getMessage());
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                iExportTaskService.updateById(exportTask);
            } finally {
                if (!dataFile.delete()) {
                    log.error("采购结算系统明细临时文件删除失败。path:{}", dataFile.getAbsolutePath());
                }
            }
        });
    }


    @Override
    public FormDetailVo viewFormInfo(FormDetailCmd cmd) {
        Assert.isTrue(CollUtil.isNotEmpty(cmd.getIds()), "id不得为空");
        List<OrderSettlementForm> formList = iOrderSettlementFormService.lambdaQuery()
                .in(OrderSettlementForm::getId, cmd.getIds()).list();
        Assert.isTrue(CollUtil.isNotEmpty(formList), "id非法");

        FormDetailVo vo = new FormDetailVo();

        OrderSettlementForm form0 = formList.get(0);
        List<Long> providerIds = ListUtil.of(form0.getSProviderId(), form0.getPProviderId());
        final List<Provider> providers = iProviderService.lambdaQuery().in(Provider::getId, providerIds).list();
        Map<Long, Provider> providerMap = providers
                .stream().collect(Collectors.toMap(Provider::getId, Function.identity()));

        final Set<Long> partnerProviderIds = providers.stream().map(Provider::getPartnerProviderId).collect(Collectors.toSet());
        final Map<Long, PartnerProviderResp> parnterProviderMap = providerGateway.partnerBatchQueryByIds(partnerProviderIds);

        final Optional<Provider> pProvider = Optional.ofNullable(providerMap.get(form0.getPProviderId()));
        final Optional<PartnerProviderResp> pProviderPartnerData =
                pProvider.map(Provider::getPartnerProviderId).map(parnterProviderMap::get);

        final Optional<Provider> sProvider = Optional.ofNullable(providerMap.get(form0.getSProviderId()));
        final Optional<PartnerProviderResp> sProviderPartnerData =
                sProvider.map(Provider::getPartnerProviderId).map(parnterProviderMap::get);

        vo.setPProviderId(form0.getPProviderId());
        vo.setPProviderName(pProvider.map(Provider::getName).orElse(""));
        vo.setPProviderIsBlacklist(pProviderPartnerData.map(PartnerProviderResp::getIsBlacklist).orElse(0));

        vo.setSProviderId(form0.getSProviderId());
        vo.setSProviderName(sProvider.map(Provider::getName).orElse(""));
        vo.setSProviderIsBlacklist(sProviderPartnerData.map(PartnerProviderResp::getIsBlacklist).orElse(0));

        vo.setPOrgId(103L);
        vo.setPOrgName("杭州老爸电商科技有限公司");
        vo.setBusinessLine(form0.getBusinessLine());

        List<PurchaseOrderDo> purchaseOrders = new ArrayList<>();
        List<String> purchaseOrderNos = formList.stream().map(val -> {
            if (StringUtils.hasText(val.getPurchaseOrderNo())) {
                return Arrays.asList(val.getPurchaseOrderNo().split(","));
            }
            return new ArrayList<String>();
        }).distinct().flatMap(List::stream).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(purchaseOrderNos)) {
            purchaseOrders = iPurchaseOrderService.lambdaQuery().in(PurchaseOrder::getNo, purchaseOrderNos)
                    .list().stream().map(val -> new PurchaseOrderDo(val.getId(), val.getNo())).collect(Collectors.toList());
        }
        vo.setPurchaseOrders(purchaseOrders);
        List<String> warehouseNos = formList.stream().map(val -> {
            if (StringUtils.hasText(val.getWarehouseNo())) {
                return Arrays.asList(val.getWarehouseNo().split(","));
            }
            return new ArrayList<String>();
        }).distinct().flatMap(List::stream).collect(Collectors.toList());
        List<WarehouseDO> warehouseDos = iWarehouseService.queryWarehouseMapByNo(warehouseNos);
        String warehouseNames = warehouseDos.stream().map(WarehouseDO::getName).collect(Collectors.joining(","));
        vo.setWarehouseName(warehouseNames);
        Map<String, List<String>> opMap = iWarehouseService.getOrderPersonnelNickName(warehouseNos);
        String opNames = opMap.values().stream().flatMap(List::stream).distinct().collect(Collectors.joining(","));
        vo.setOrderPersonnel(opNames);

        vo.setUpdatedTime(0L);

        List<DetailStaticDo> staticList = formList.stream()
                .map(val -> JsonUtil.parse(val.getStaticInfo(), DetailStaticDo.class)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(staticList)) {
            if (staticList.size() == 1) {
                vo.setStaticDo(staticList.get(0));
            } else {
                vo.setStaticDo(staticListMerge(staticList));
            }
        } else {
            vo.setStaticDo(new DetailStaticDo());
        }

        if (Objects.equals(Boolean.TRUE, cmd.getIsAdd())) {
            vo.setNo("");
            vo.setSelectBillIds(cmd.getIds());
        } else {
            vo.setNo("");
            vo.setSelectBillIds(cmd.getIds());
            if (cmd.getIds().size() == 1) {
                if (form0.getStatus().equals(OrderSettlementStatus.CONFIRMED)) {
                    vo.setNo(form0.getNo());
                    if (StringUtils.hasText(form0.getRelateId())) {
                        List<Long> collect = Arrays.stream(form0.getRelateId().split(",")).map(Long::valueOf).collect(Collectors.toList());
                        vo.setSelectBillIds(collect);
                    }
                    vo.setPayApplyStatus(getPayApplyStatus(form0.getNo()));
                }
            }
        }

        Map<Long, List<LocalDateTime>> sortTimeMap = getSortTimeMap(Collections.singletonList(form0.getId()));
        List<LocalDateTime> localDateTimes = sortTimeMap.get(form0.getId());
        vo.setCycleDate(DateUtil.polymerizationTime(localDateTimes));

        vo.setVersion(Objects.isNull(form0.getVersion()) ? 1 : form0.getVersion());

        return vo;
    }

    private DetailStaticDo staticListMerge(List<DetailStaticDo> staticList) {
        return staticList.stream().reduce(DetailStaticDo.emptyOne(), this::mergeDetailStatic);
    }

    private DetailStaticDo mergeDetailStatic(DetailStaticDo d1, DetailStaticDo d2) {
        DetailStaticDo t = DetailStaticDo.emptyOne();
        t.setTsTemporaryQuantity(d1.getTsTemporaryQuantity() + d2.getTsTemporaryQuantity());
        t.setTsDeliverQuantity(d1.getTsDeliverQuantity() + d2.getTsDeliverQuantity());
        t.setTsCurrentMonthRefundQuantity(d1.getTsCurrentMonthRefundQuantity() + d2.getTsCurrentMonthRefundQuantity());
        t.setTsCrossMonthRefundQuantity(d1.getTsCrossMonthRefundQuantity() + d2.getTsCrossMonthRefundQuantity());
        t.setTsSettlementQuantity(d1.getTsSettlementQuantity() + d2.getTsSettlementQuantity());
        t.setTsSettlementAmount(d1.getTsSettlementAmount().add(d2.getTsSettlementAmount()));
        t.setTsFinalAmount(d1.getTsFinalAmount().add(d2.getTsFinalAmount()));
        t.setTsRemark("");
        t.setTransportAmount(d1.getTransportAmount().add(d2.getTransportAmount()));
        t.setTransportFinalAmount(d1.getTransportFinalAmount().add(d2.getTransportFinalAmount()));
        t.setTransportRemark("");
        t.setAfterSalesAmount(d1.getAfterSalesAmount().add(d2.getAfterSalesAmount()));
        t.setAfterSalesFinalAmount(d1.getAfterSalesFinalAmount().add(d2.getAfterSalesFinalAmount()));
        t.setAfterSalesRemark("");
        t.setOtherAmount(d1.getOtherAmount().add(d2.getOtherAmount()));
        t.setOtherFinalAmount(d1.getOtherFinalAmount().add(d2.getOtherFinalAmount()));
        t.setOtherRemark("");
        t.setTotalSettlementQuantity(d1.getTotalSettlementQuantity() + d2.getTsSettlementQuantity());
        t.setTotalSettlementAmount(d1.getTotalSettlementAmount().add(d2.getTsSettlementAmount()));
        t.setTotalFinalAmount(d1.getTotalFinalAmount().add(d2.getTotalFinalAmount()));
        t.setTotalRemark("");
        t.setTotalTemporaryQuantity(d1.getTotalTemporaryQuantity() + d2.getTsTemporaryQuantity());
        return t;
    }

    @Override
    public PageResponse<FormDetailPageVo> viewFormDetailPage(FormDetailPageQuery query) {
        PageInfo<OrderSettlementDetail> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> iOrderSettlementDetailService.lambdaQuery()
                        .in(CollUtil.isNotEmpty(query.getIds()), OrderSettlementDetail::getFormId, query.getIds())
                        .list());
        if (pageInfo.getTotal() == 0) {
            return PageResponse.of(new LinkedList<>(), 0, query.getPageSize(), query.getPageIndex());
        }

        Map<String, String> skuNameMap = new HashMap<>(16);
        Map<String, List<ItemSku>> skuMap = new HashMap<>(16);
        Map<String, List<ItemSku>> skuMap2 = new HashMap<>(16);
        Map<String, List<CorpBizTypeDTO>> divisionMap = new HashMap<>(16);
        Set<String> skuCodes = pageInfo.getList().stream().map(OrderSettlementDetail::getSkuCode).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(skuCodes)) {
            skuNameMap = itemSkuMapper.selectSkuName(skuCodes)
                    .stream().collect(Collectors.toMap(SkuNameDo::getSkuCode, SkuNameDo::getName));
            skuMap = iItemSkuService.lambdaQuery()
                    .in(ItemSku::getSkuCode, skuCodes)
                    .list()
                    .stream().collect(Collectors.groupingBy(ItemSku::getSkuCode));
            skuMap2 = iItemSkuService.lambdaQuery()
                    .in(ItemSku::getProviderSpecifiedCode, skuCodes)
                    .list()
                    .stream().collect(Collectors.groupingBy(ItemSku::getProviderSpecifiedCode));
            divisionMap = iBizLevelDivisionService.queryBySkuCode(skuCodes);
        }

        Map<String, String> finalSkuNameMap = skuNameMap;
        Map<String, List<ItemSku>> finalSkuMap = skuMap;
        Map<String, List<ItemSku>> finalSkuMap2 = skuMap2;
        Map<String, List<CorpBizTypeDTO>> finalDivisionMap = divisionMap;
        return PageResponse.of(pageInfo.getList().stream().map(val -> {
            FormDetailPageVo vo = new FormDetailPageVo();
            vo.setId(val.getId());
            Long settlementStartDate = val.getSettlementStartDate();
            Long valSettlementEndDate = val.getSettlementEndDate();
            if (Objects.isNull(settlementStartDate) && Objects.isNull(valSettlementEndDate)) {
                vo.setCycle("");
            } else {
                String cycle = DateUtil.parseTimeStamp(settlementStartDate, DatePattern.NORM_MONTH_PATTERN);
                vo.setCycle(cycle);
            }
            vo.setSettlementStartDate(settlementStartDate);
            vo.setSettlementEndDate(valSettlementEndDate);
            vo.setSkuCode(val.getSkuCode());
            vo.setItemName(finalSkuNameMap.getOrDefault(val.getSkuCode(), ""));
            List<ItemSku> itemSkus = CollUtil.isNotEmpty(finalSkuMap.get(val.getSkuCode())) ?
                    finalSkuMap.get(val.getSkuCode()) : finalSkuMap2.get(val.getSkuCode());
            if (CollUtil.isNotEmpty(itemSkus)) {
                vo.setSpecifications(itemSkus.get(0).getSpecifications());
                vo.setUnit(itemSkus.get(0).getUnit());
            }
            vo.setTemporaryPrice(val.getTemporaryPrice());
            vo.setTemporaryQuantity(val.getTemporaryQuantity());
            vo.setDeliverQuantity(val.getDeliverQuantity());
            vo.setCurrentMonthRefundQuantity(val.getCurrentMonthRefundQuantity());
            vo.setCrossMonthRefundQuantity(val.getCrossMonthRefundQuantity());
            vo.setSettlementPrice(val.getSettlementPrice());
            vo.setSettlementQuantity(val.getSettlementQuantity());
            vo.setSettlementAmount(val.getSettlementAmount());
            vo.setAfterSalesCost(val.getAfterSalesCost());
            vo.setFinalAmount(val.getFinalAmount());
            vo.setRemark(val.getRemark());
            vo.setSource(val.getSource());
            vo.setFormId(val.getFormId());
            vo.setFormNo(val.getFormNo());
            vo.setCorpBizType(finalDivisionMap.get(val.getSkuCode()));

//            if (appliedDetailIdList.contains(vo.getId())) {
//                vo.setIsPayApplied(1);
//            } else {
//                vo.setIsPayApplied(0);
//            }

            return vo;
        }).collect(Collectors.toList()), (int) pageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    @Override
    public void save(SettlementSaveCmd cmd) {
        // 暂时去除校验
//        List<DetailExcelEditCmd> detailList = cmd.getDetailList();
//        if (CollUtil.isNotEmpty(detailList)) {
//            detailList.forEach(DetailExcelEditCmd::dataCheck);
//        }
        if (cmd.getIsAdd()) {
            addHandler(cmd);
        } else {
            Assert.state(CollUtil.isNotEmpty(cmd.getIds()) && cmd.getIds().size() == 1, "数据异常，ids只能唯一");
            Long formId = cmd.getIds().get(0);
            editProcess(formId, cmd);
        }
    }

    @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER, msg = "此单据可能他人正在编辑，请稍后再试", waitTime = 0)
    private void editProcess(@DistributedLockKey Long formId, SettlementSaveCmd cmd) {
        OrderSettlementForm form0 = iOrderSettlementFormService.getById(formId);
        List<String> logList = new LinkedList<>();

        if (!form0.getPProviderId().equals(cmd.getPurProviderId())) {
            Provider oldOne = iProviderService.getById(form0.getPProviderId());
            Provider newOne = iProviderService.getById(cmd.getPurProviderId());
            String s = "采购供应商改变，" + (Objects.nonNull(oldOne) ? oldOne.getName() : "") + "变为" + (Objects.nonNull(newOne) ? newOne.getName() : "");
            logList.add(s);
        }
        if (!form0.getSProviderId().equals(cmd.getSettProviderId())) {
            Provider oldOne = iProviderService.getById(form0.getSProviderId());
            Provider newOne = iProviderService.getById(cmd.getSettProviderId());
            String s = "结算供应商改变，" + (Objects.nonNull(oldOne) ? oldOne.getName() : "") + "变为" + (Objects.nonNull(newOne) ? newOne.getName() : "");
            logList.add(s);
        }
        form0.setSProviderId(cmd.getSettProviderId());
        form0.setPProviderId(cmd.getPurProviderId());
        String providerUpdateLog = providerUpdateLog(cmd, form0);

        // 查看-编辑-保存。点击【结算修改】，店家保存
        // 更新单据关联关系，删除旧明细，查询新账单明细，记录新的关联关系。
        if (cmd.getSettlementChange()) {
            Assert.state(CollUtil.isNotEmpty(cmd.getNewBillIds()), "[结算修改]新的账单ID不得为空");
            List<Long> newBillIds = cmd.getNewBillIds();
            List<DetailExcelEditCmd> detailList = cmd.getDetailList();
            Assert.state(CollUtil.isNotEmpty(detailList), "[结算修改]新的账单明细不得为空");
            // 根据新账单的数据，更新旧账单数据
            List<OrderSettlementForm> newForms = iOrderSettlementFormService.lambdaQuery()
                    .in(OrderSettlementForm::getId, cmd.getNewBillIds()).list();
            Assert.isTrue(CollUtil.isNotEmpty(newForms), "[结算修改]新的账单ID非法");
            long newFormsBlKindCounts = newForms.stream().map(OrderSettlementForm::getBusinessLine).distinct().count();
//            Assert.isTrue(newFormsBlKindCounts == 1, "[结算修改]禁止跨合作模式混合结算");
            Assert.isTrue(Objects.equals(newForms.get(0).getBusinessLine(), form0.getBusinessLine()),
                    "[结算修改]新增单据的合作模式和原单据不一致");

            // 随机选一个新的系统账单的数据作为id.
            OrderSettlementForm oneForm = iOrderSettlementFormService.getById(newBillIds.get(0));
            form0.setPProviderId(oneForm.getPProviderId());
            form0.setSProviderId(oneForm.getSProviderId());

            String oldRelateId = form0.getRelateId();
            form0.setRelateId(CollUtil.join(newBillIds, ","));

            List<String> ps = new LinkedList<>();
            List<String> ws = new LinkedList<>();
            for (OrderSettlementForm newForm : newForms) {
                ps.add(newForm.getPurchaseOrderNo());
                ws.add(newForm.getWarehouseNo());
            }
            form0.setPurchaseOrderNo(CollUtil.join(ps, ","));
            form0.setWarehouseNo(CollUtil.join(ws, ","));

            List<OrderSettlementDetail> newDetails = detailList.stream().map(detailCmd -> {
                OrderSettlementDetail detail = baseConvert(detailCmd);
                detail.setFormNo(form0.getNo());
                detail.setFormId(form0.getId());
                detail.setTemporaryPrice(detailCmd.getTemporaryPrice());
                detail.setTemporaryQuantity(detailCmd.getTemporaryQuantity());
                return detail;
            }).collect(Collectors.toList());

            // 从新的明细列表中，选出头尾时间作为结算单时间
            Optional<OrderSettlementDetail> sDate = newDetails.stream().min(Comparator.comparing(OrderSettlementDetail::getSettlementStartDate));
            sDate.ifPresent(orderSettlementDetail -> form0.setSettlementStartDate(orderSettlementDetail.getSettlementStartDate()));
            Optional<OrderSettlementDetail> eDate = newDetails.stream().max(Comparator.comparing(OrderSettlementDetail::getSettlementEndDate));
            eDate.ifPresent(orderSettlementDetail -> form0.setSettlementEndDate(orderSettlementDetail.getSettlementEndDate()));

            form0.setStaticInfo(JsonUtil.toJson(cmd.getDetailStaticDo()));
            orderSettlementDao.settlementChangeUpdate(form0, newDetails, oldRelateId, newBillIds);

            return;
        }

        // addDetails
        List<DetailExcelEditCmd> addCmdDetailList = cmd.getDetailList().stream()
                .filter(val -> Objects.isNull(val.getId())).collect(Collectors.toList());
        List<OrderSettlementDetail> addDetailList = addCmdDetailList.stream().map(detailCmd -> {
            OrderSettlementDetail detail = baseConvert(detailCmd);
            detail.setFormNo(form0.getNo());
            detail.setFormId(form0.getId());
            return detail;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addDetailList)) {
            String skuCodeStr = addCmdDetailList.stream().map(DetailExcelEditCmd::getSkuCode).collect(Collectors.joining(StrUtil.COMMA));
            String s = StrUtil.format("新增sku明细。sku编码:{}", skuCodeStr);
            logList.add(s);
        }
        // updateDetails
        List<DetailExcelEditCmd> updateCmdDetailList = cmd.getDetailList().stream()
                .filter(val -> Objects.nonNull(val.getId())).collect(Collectors.toList());
        List<OrderSettlementDetail> updateDetailList = updateCmdDetailList.stream().map(detailCmd -> {
            OrderSettlementDetail detail = baseConvert(detailCmd);
            detail.setId(detailCmd.getId());
            detail.setFormNo(form0.getNo());
            return detail;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateDetailList)) {
            List<String> list = updateDetailLogs(form0.getId(), updateDetailList);
            logList.addAll(list);
        }
        // removeIds
        List<Long> removeIds = cmd.getRemoveIds();
        if (CollUtil.isNotEmpty(removeIds)) {
            String skuCodeStr = iOrderSettlementDetailService.lambdaQuery().eq(OrderSettlementDetail::getId, removeIds)
                    .select(OrderSettlementDetail::getSkuCode).list().stream().map(OrderSettlementDetail::getSkuCode)
                    .collect(Collectors.joining(StrUtil.COMMA));
            String s = StrUtil.format("移除sku明细。sku编码:{}", skuCodeStr);
            logList.add(s);
        }
        if (StrUtil.isNotBlank(form0.getStaticInfo())) {
            DetailStaticDo oldOne = JsonUtil.parse(form0.getStaticInfo(), DetailStaticDo.class);
            if (Objects.nonNull(oldOne)) {
                String staticsLog = getStaticsLog(oldOne, cmd.getDetailStaticDo());
                logList.add(staticsLog);
            }
        }
        form0.setStaticInfo(JsonUtil.toJson(cmd.getDetailStaticDo()));
        int version = Objects.isNull(form0.getVersion()) ? 1 : form0.getVersion();
        form0.setVersion(version + 1);
        orderSettlementDao.updateOrderSettlement(form0, addDetailList, updateDetailList, removeIds, logList);
    }

    private String providerUpdateLog(SettlementSaveCmd cmd, OrderSettlementForm form) {
        if (!Objects.equals(cmd.getPurProviderId(), form.getPProviderId())
                && !Objects.equals(cmd.getSettProviderId(), form.getSProviderId())) {
            Set<Long> providerIds = ListUtil.of(cmd.getPurProviderId(), form.getPProviderId(), cmd.getSettProviderId(), form.getSProviderId())
                    .stream().filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, String> providerNameMap = iProviderService.lambdaQuery().in(Provider::getId, providerIds)
                    .select(Provider::getId, Provider::getName).list()
                    .stream().collect(Collectors.toMap(Provider::getId, Provider::getName));

            StringBuilder logBuilder = new StringBuilder();
            String logPrefix = UserContext.getNickName();
            StringBuilder s1 = new StringBuilder();
            if (!Objects.equals(cmd.getPurProviderId(), form.getPProviderId())) {
                String opp = Objects.isNull(form.getPProviderId()) ? "" :
                        providerNameMap.getOrDefault(form.getPProviderId(), "xxx供应商");
                String npp = Objects.isNull(cmd.getPurProviderId()) ? "" :
                        providerNameMap.getOrDefault(cmd.getPurProviderId(), "xxx供应商");
                s1.append("修改了采购供应商，将").append(opp).append("改为了").append(npp);
            }
            StringBuilder s2 = new StringBuilder();
            if (!Objects.equals(cmd.getSettProviderId(), form.getSProviderId())) {
                String osp = Objects.isNull(form.getSProviderId()) ? "" :
                        providerNameMap.getOrDefault(form.getSProviderId(), "xxx供应商");
                String nsp = Objects.isNull(cmd.getSettProviderId()) ? "" :
                        providerNameMap.getOrDefault(cmd.getSettProviderId(), "xxx供应商");
                s2.append("修改了结算供应商，将").append(osp).append("改为了").append(nsp);
            }
            logBuilder.append(logPrefix).append(s1);
            if (StringUtils.hasText(s2)) {
                logBuilder.append(",").append(s2);
            }
            return logBuilder.toString();
        }
        return "";
    }

    private Map<String, BigDecimal> getFiledMap(List<OrderSettlementDetail> list,
                                                Function<OrderSettlementDetail, BigDecimal> function) {
        return list.stream().collect(Collectors
                .toMap(OrderSettlementDetail::getSkuCode, function, (a, b) -> a));
    }

    public static void main(String[] args) {
        DetailStaticDo d1 = new DetailStaticDo();
        d1.setDiffId(1L);
        d1.setOtherAmount(new BigDecimal(22));
        d1.setTsSettlementQuantity(1);

        DetailStaticDo d2 = new DetailStaticDo();
        d2.setDiffId(1L);
        d2.setOtherAmount(new BigDecimal(23.000000));
        d2.setTsSettlementQuantity(11);


        Diff diff = DiffUtil.diff(d1, d2);
        Changes changes = diff.getChanges();

        System.out.println(DiffUtil.getVerboseDiffLog(diff, DetailStaticDo.class, ""));
    }

    private String getStaticsLog(DetailStaticDo oldOne, DetailStaticDo newOne) {

        oldOne.setDiffId(1L);
        newOne.setDiffId(1L);
        Diff diff = DiffUtil.diff(oldOne, newOne);
        return DiffUtil.getVerboseDiffLog(diff, DetailStaticDo.class, "");
    }

    private List<String> updateDetailLogs(Long oldFormId, List<OrderSettlementDetail> newDetailList) {
        List<String> logList = new LinkedList<>();
        List<OrderSettlementDetail> oldDetailList = iOrderSettlementDetailService.lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, oldFormId).list();
        DiffUtil.ObjListDiff detailDiffList = DiffUtil.diffObjList(oldDetailList, newDetailList, OrderSettlementDetail.class);
        Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = detailDiffList.getChangeMap();
        changeMap.forEach((k, setVal) -> {
            String valStr = setVal.stream().map(val -> StrUtil.format("{}从{}改为{}", val.getProperty(), val.getOldVal(), val.getNewVal())).collect(Collectors.joining(StrUtil.COMMA));
            String ss = StrUtil.format("sku:{},{}", k, valStr);
            logList.add(ss);
        });

        return logList;
    }

//    private String detailUpdateLog(SettlementSaveCmd cmd, OrderSettlementForm form) {
//
//        StringBuilder staticLog = new StringBuilder();
//        List<String> changeValues = new LinkedList<>();
//        DetailStaticDo oldDo = JsonUtil.parse(form.getStaticInfo(), DetailStaticDo.class);
//        Diff diff = DiffUtil.diff(oldDo, cmd.getDetailStaticDo());
//        if (diff.hasChanges()) {
//            Changes changes = diff.getChanges();
//            for (Change change : changes) {
//                ValueChange valueChange = (ValueChange) change;
//                changeValues.add(valueChange.getPropertyName());
//            }
//        }
//        List<OrderSettlementDetail> oldDetailList = iOrderSettlementDetailService.lambdaQuery()
//                .eq(OrderSettlementDetail::getFormId, form.getId()).list();
//        Map<String, BigDecimal> oldSettlementPriceMap = getFiledMap(oldDetailList, OrderSettlementDetail::getSettlementPrice);
//        Map<String, BigDecimal> oldAfterSalesCostMap = getFiledMap(oldDetailList, OrderSettlementDetail::getAfterSalesCost);
//        List<DetailExcelEditCmd> detailList = cmd.getDetailList();
//        Map<String, BigDecimal> newSettlementPriceMap = detailList.stream().collect(Collectors
//                .toMap(DetailExcelEditCmd::getSkuCode, DetailExcelEditCmd::getSettlementPrice, (a, b) -> a));
//        Map<String, BigDecimal> newAfterSalesCostMap = detailList.stream().collect(Collectors
//                .toMap(DetailExcelEditCmd::getSkuCode, DetailExcelEditCmd::getAfterSalesCost, (a, b) -> a));
//        Diff priceDiff = DiffUtil.diff(oldSettlementPriceMap, newSettlementPriceMap, false);
//        if (priceDiff.hasChanges()) {
//            changeValues.add("结算单价");
//        }
//        Diff costDiff = DiffUtil.diff(oldAfterSalesCostMap, newAfterSalesCostMap, false);
//        if (costDiff.hasChanges()) {
//            changeValues.add("运费及售后分摊金额");
//        }
//        if (CollUtil.isNotEmpty(changeValues)) {
//            staticLog.append(UserContext.getNickName()).append("修改了").append(String.join(",", changeValues));
//            return staticLog.toString();
//        }
//
//        return "";
//    }


    private void addHandler(SettlementSaveCmd cmd) {
        List<RLock> idLockContainer = new ArrayList<>();
        String formNo = addLockProcess(cmd.getIds(), idLockContainer);
        try {
            Assert.hasText(formNo, "新增结算单，编码不得为空");
            addProcess(formNo, cmd);
        } catch (Exception e) {
            log.error("save order settlement error", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "保存信息失败。" + e.getMessage());
        } finally {
            unlock(idLockContainer);
        }
    }

    private String addLockProcess(List<Long> ids, List<RLock> idLockContainer) {
        Set<Long> idSet = new HashSet<>(ids);
        RLock addLock = redissonClient.getLock(LOCK_BILL);
        Assert.notNull(addLock, "数据锁不得为空");
        boolean getAllIdLockSuccess = true;
        try {
            // 防止：A选择了id1,2,4的单据新增，B选择了id4,5,6的单据新增。id4就发生了数据冲突，防止这种情况发生
            // 先锁住整个保存操作，然后从ID纬度锁尝试锁住每个单据，只有获取全部的ID的锁才能执行保存。
            addLock.lock();
            for (Long id : idSet) {
                RLock idLock = redissonClient.getLock(LOCK_BILL + "_" + id);
                try {
                    boolean b = idLock.tryLock();
                    if (b) {
                        idLockContainer.add(idLock);
                    } else {
                        getAllIdLockSuccess = false;
                        break;
                    }
                } catch (Exception e) {
                    idLock.unlock();
                    getAllIdLockSuccess = false;
                    break;
                }
            }
            if (!getAllIdLockSuccess) {
                unlock(idLockContainer);
                throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "有部分单据正在处理中，可能他人正在操作，请稍后再试");
            }

            // 串行操作获取结算单编码
            try {
                String noPrefix = NO_PREFIX + DateUtil.format(LocalDateTime.now(), DatePattern.SIMPLE_MONTH_PATTERN);
                String latestNo = orderSettlementFormMapper.maxNo(noPrefix);
                String formNo;
                if (StringUtils.hasText(latestNo)) {
                    long v1 = parseLong(latestNo.replaceAll(noPrefix, "").trim()) + 1;
                    formNo = noPrefix + String.format("%06d", v1);
                } else {
                    formNo = noPrefix + "000001";
                }
                return formNo;
            } catch (Exception e) {
                unlock(idLockContainer);
                log.error("get order settlement no error", e);
                throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "获取结算单编码异常");
            }
        } finally {
            if (addLock.isLocked()) {
                addLock.unlock();
            }
        }
    }

    private void unlock(List<RLock> idLocks) {
        if (CollUtil.isNotEmpty(idLocks)) {
            idLocks.forEach(rr -> {
                if (rr.isLocked()) {
                    rr.unlock();
                }
            });
        }
    }

    private void addProcess(String thisFormNo, SettlementSaveCmd cmd) {
        List<OrderSettlementForm> list = iOrderSettlementFormService.lambdaQuery().in(OrderSettlementForm::getId, cmd.getIds()).list();

        long count = list.stream().map(OrderSettlementForm::getBusinessLine).distinct().count();
//        Assert.isTrue(count == 1, "禁止跨合作模式混合结算");

        String addLog = UserContext.getNickName() + "新增了（工厂）采购结算单";

        long startTime = 0L;
        long endTime = 0L;
        Optional<OrderSettlementForm> earliestOpt = list.stream().min(Comparator.comparing(OrderSettlementForm::getSettlementStartDate));
        if (earliestOpt.isPresent()) {
            startTime = earliestOpt.get().getSettlementStartDate();
        }
        Optional<OrderSettlementForm> latestOpt = list.stream().max(Comparator.comparing(OrderSettlementForm::getSettlementEndDate));
        if (latestOpt.isPresent()) {
            endTime = latestOpt.get().getSettlementEndDate();
        }
        String warehouseNosStr = list.stream().map(OrderSettlementForm::getWarehouseNo).distinct().collect(Collectors.joining(","));
        int temporaryQuantity = list.stream().mapToInt(OrderSettlementForm::getTemporaryQuantity).sum();
        int settlementQuantity = list.stream().mapToInt(OrderSettlementForm::getSettlementQuantity).sum();
        String purchaseOrderNos = list.stream().map(OrderSettlementForm::getPurchaseOrderNo).collect(Collectors.joining(","));
        OrderSettlementForm addForm = new OrderSettlementForm();
        addForm.setNo(thisFormNo);
        addForm.setPurchaseOrderNo(purchaseOrderNos);
        addForm.setSettlementStartDate(startTime);
        addForm.setSettlementEndDate(endTime);
        addForm.setPProviderId(cmd.getPurProviderId());
        addForm.setSProviderId(cmd.getSettProviderId());
        addForm.setWarehouseNo(warehouseNosStr);
        addForm.setStatus(OrderSettlementStatus.CONFIRMED);
        addForm.setTemporaryQuantity(temporaryQuantity);
        addForm.setSettlementQuantity(settlementQuantity);
        addForm.setStaticInfo(JsonUtil.toJson(cmd.getDetailStaticDo()));
        addForm.setRelateId(CollUtil.join(cmd.getIds(), StrUtil.COMMA));
        addForm.setBusinessLine(list.get(0).getBusinessLine());
        List<OrderSettlementDetail> addDetailList = cmd.getDetailList().stream().map(detailCmd -> {
            OrderSettlementDetail detail = baseConvert(detailCmd);
            detail.setFormNo(thisFormNo);
            detail.setTemporaryPrice(detailCmd.temporaryPrice);
            detail.setTemporaryQuantity(detailCmd.getTemporaryQuantity());
            return detail;
        }).collect(Collectors.toList());

        orderSettlementDao.addOrderSettlement(addForm, addDetailList, addLog, cmd.getIds());
    }


    private OrderSettlementDetail baseConvert(DetailExcelEditCmd detailCmd) {
        OrderSettlementDetail detail = new OrderSettlementDetail();
        detail.setSettlementStartDate(detailCmd.getTime());
        detail.setSettlementEndDate(detailCmd.getTime());
        detail.setSkuCode(detailCmd.getSkuCode());
        detail.setDeliverQuantity(detailCmd.getDeliverQuantity());
        detail.setCurrentMonthRefundQuantity(detailCmd.getCurrentMonthRefundQuantity());
        detail.setCrossMonthRefundQuantity(detailCmd.getCrossMonthRefundQuantity());
        detail.setSettlementQuantity(detailCmd.getSettlementQuantity());
        detail.setSettlementPrice(detailCmd.getSettlementPrice());
        detail.setSettlementAmount(detailCmd.getSettlementAmount());
        detail.setAfterSalesCost(detailCmd.getAfterSalesCost());
        detail.setFinalAmount(detailCmd.getFinalAmount());
        detail.setRemark(detailCmd.getRemark());
        detail.setSource(detailCmd.getSource());
        return detail;
    }


    @Override
    public void exportSettlement(Long id) {

        OrderSettlementForm form = iOrderSettlementFormService.getById(id);
        Assert.notNull(form, "id非法");
        Long providerId = form.getSProviderId();
        Provider provider = iProviderService.getById(providerId);
        String filePrefix = String.format("%s_老爸评测【%s】订货明细单", form.getNo(), provider.getName());
        filePrefix = filePrefix.replaceAll(" ", "");

        ExportTask exportTask = ExportTask.initTask(filePrefix, ExportTaskType.ALL_SETTLEMENT_INFO);
        iExportTaskService.save(exportTask);

        String finalFilePrefix = filePrefix;
        singleExecutor.submit(() -> settlementExport0(exportTask, finalFilePrefix, form));
    }

    private File getExportFile0(String filePrefix, OrderSettlementForm form) {

        File settlementTmpFile;
        ExcelWriter settlementWriter = null;
        boolean settlementExportRes;
        try {
            settlementTmpFile = new File("settlementTmp_" + RandomUtil.randomString(4) + ".xlsx");
            File exportExcelTemplate = getExportExcelTemplate(SETTLEMENT_ALL_NAME);
            settlementWriter = EasyExcelFactory.write(settlementTmpFile)
                    .withTemplate(exportExcelTemplate.getAbsolutePath())
                    .inMemory(true).build();
            settlementExportRes = exportSettlementOrder(form.getId(), form.getStaticInfo(), filePrefix, settlementWriter);
            log.info("结算导出-结算单写入完毕.formId:{},result:{}", form.getId(), settlementExportRes);
        } catch (Exception e) {
            log.error("结算导出-结算单写入异常.formId:{}", form.getId(), e);
            throw e;
        } finally {
            if (null != settlementWriter) {
                settlementWriter.finish();
            }
        }
        Assert.isTrue(settlementExportRes, "结算导出-结算单写入不得失败");
        log.info("结算导出-结算单临时文件生成完毕.formId:{},time:{}", form.getId(), DateUtil.currentTime());

        File completeExportFile;
        try {
            completeExportFile = new File(filePrefix + "_" + RandomUtil.randomString(4) + ".xlsx");
            ExcelWriter completeExcelWriter = EasyExcel.write(completeExportFile).withTemplate(settlementTmpFile).file(completeExportFile).build();
            // 发货明细
            boolean b2 = deliverDetail(form.getId(), form.getWarehouseNo(), form.getRelateId(), completeExcelWriter);
            log.info("结算导出-发货明细写入完毕.formId:{},result:{}", form.getId(), b2);
            // 退货明细
            boolean b3 = exportRefundMange(form.getId(), form.getWarehouseNo(), completeExcelWriter);
            log.info("结算导出-退货明细写入完毕.formId:{},result:{}", form.getId(), b3);
            // 客服售后明细。
            boolean b4 = afterSalesDetail(form.getWarehouseNo(), form.getId(), completeExcelWriter);
            log.info("结算导出-客服售后明细写入完毕.formId:{},result:{}", form.getId(), b4);
            if (b2 & b3 & b4) {
                completeExcelWriter.finish();
            } else {
                String errorLog = StrUtil.format("结算导出Excel写入失败.formId:{}", form.getId());
                throw new RuntimeException(errorLog);
            }
        } catch (Exception e) {
            log.error("结算导出Excel写入异常.formId:{}", form.getId(), e);
            String errorLog = StrUtil.format("结算导出Excel写入异常.formId:{}", form.getId());
            throw new RuntimeException(errorLog);
        } finally {
            FileUtil.del(settlementTmpFile);
        }
        return completeExportFile;
    }

    private void addSheet(File existingFile) {
        File file = new File("addsheet.xlsx");

        List<PurchaseBillRow> data = new ArrayList<>();
        PurchaseBillRow purchaseBillRow = new PurchaseBillRow();
        purchaseBillRow.setOrderNo("111");
        data.add(purchaseBillRow);
        WriteSheet refundDetailSheet = EasyExcel.writerSheet("退货明细").head(PurchaseBillRow.class).build();


        ExcelWriter excelWriter = EasyExcel.write(file).withTemplate(existingFile).file(file).build();
        excelWriter.write(data, refundDetailSheet);
        excelWriter.finish();

    }

    private void settlementExport0(ExportTask exportTask, String filePrefix, OrderSettlementForm form) {
        File excelFile = null;
        try {
            excelFile = getExportFile0(filePrefix, form);
            UploadFileAction action = UploadFileAction.ofFile(File.separator + filePrefix + "_" +
                    RandomUtil.randomString(4) + ".xlsx", excelFile);
            String url = fileGateway.uploadFile(action).getUrl();
            exportTask.setDownloadUrl(url);
            exportTask.setStatus(ExportTaskStatus.SUCCESS);
            iExportTaskService.updateById(exportTask);
        } catch (Exception e) {
            log.error("结算导出处理异常", e);
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError("结算导出处理异常");
            iExportTaskService.updateById(exportTask);
        } finally {
            FileUtil.del(excelFile);
        }
    }


    @Override
    public void exportSettlementPlus(BatchExportCmd cmd) {

        Assert.isTrue(CollUtil.isNotEmpty(cmd.getIds()), "导出id数组不得为空");
        List<Long> ids = cmd.getIds();
        List<OrderSettlementForm> list = iOrderSettlementFormService.lambdaQuery().in(OrderSettlementForm::getId, ids).list();
        Assert.isTrue(CollUtil.isNotEmpty(list), "目标id数组非法，查不到对应的采购结算单据");

        String generalTaskCode = RandomUtil.randomString(6);
        ExportTask generalTask = ExportTask.initTask(generalTaskCode + "_", ExportTaskType.ALL_SETTLEMENT_INFO_ZIP);
        iExportTaskService.save(generalTask);

        singleExecutor.submit(() -> {
            CopyOnWriteArrayList<File> successFiles = new CopyOnWriteArrayList<>();
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            AtomicInteger finishedCount = new AtomicInteger(0);
            CopyOnWriteArrayList<String> failNos = new CopyOnWriteArrayList<>();
            for (OrderSettlementForm form : list) {
                try {
                    Long providerId = form.getSProviderId();
                    Provider provider = iProviderService.getById(providerId);
                    String filePrefix = String.format("%s_老爸评测【%s】订货明细单", form.getNo(), provider.getName());
                    File formExcelFile = getExportFile0(filePrefix, form);
                    successFiles.add(formExcelFile);
                    int andIncrement = successCount.getAndIncrement();
                    log.info("exportSettlementPlus 结算导出excel成功.generalTaskCode:{},successCount:{}", generalTaskCode,
                            andIncrement);
                } catch (Exception e) {
                    log.error("exportSettlementPlus 结算导出excel异常.generalTaskCode:{},formId:{}",
                            generalTaskCode, form.getId(), e);
                    failCount.getAndIncrement();
                    failNos.add(form.getNo());
                } finally {
                    int andIncrement = finishedCount.getAndIncrement();
                    BigDecimal progress = new BigDecimal(andIncrement)
                            .divide(new BigDecimal(list.size()), 4, RoundingMode.DOWN)
                            .multiply(EXPORT_FILE_FINISHED_PROGRESS).setScale(4, RoundingMode.DOWN);
                    generalTask.setProgress(progress);
                    iExportTaskService.updateById(generalTask);
                }
            }
            log.info("exportSettlementPlus 结算导出全部处理完毕.generalTaskCode:{},successCount:{},failCount:{}",
                    generalTaskCode, successCount.get(), failCount.get());
            if (CollUtil.isNotEmpty(failNos)) {
                log.error("exportSettlementPlus 部分结算单据导出异常.generalTaskCode:{},failNos:{}",
                        generalTaskCode, JsonUtil.toJson(failNos));
                generalTask.setError("部分结算单据导出异常，重试一下");
                generalTask.setStatus(ExportTaskStatus.FAIL);
                generalTask.setProgress(BigDecimal.ONE);
                iExportTaskService.updateById(generalTask);
                return;
            } else {
                generalTask.setProgress(EXPORT_FILE_FINISHED_PROGRESS);
                iExportTaskService.updateById(generalTask);
            }

            // -------------- 所有文件打成压缩包,再上传到oss ---------------
            String randomString = RandomUtil.randomString(6);
            String zipFilePath = "settlement_zip" + File.separator + randomString + ".zip";
            File zipFile = new File(zipFilePath);
            File zip = null;
            try {
                File[] array = successFiles.toArray(new File[]{});
                zip = ZipUtil.zip(zipFile, false, array);
                UploadFileAction action = UploadFileAction.ofFile(File.separator + "settlement" + "_" + randomString + ".zip", zip);
                String zipDownloadUrl = fileGateway.uploadFile(action).getUrl();
                generalTask.setDownloadUrl(zipDownloadUrl);
                generalTask.setStatus(ExportTaskStatus.SUCCESS);
                log.info("exportSettlementPlus 下载文件压缩打包上传处理成功.generalTaskCode:{}.task:{}",
                        generalTaskCode, JsonUtil.toJson(generalTask));
            } catch (Exception e) {
                generalTask.setError(e.getMessage());
                generalTask.setStatus(ExportTaskStatus.FAIL);
                log.info("exportSettlementPlus 下载文件压缩打包上传处理异常.generalTaskCode:{}", generalTaskCode, e);
            } finally {
                generalTask.setProgress(BigDecimal.ONE);
                iExportTaskService.updateById(generalTask);
                for (File excelLocalFile : successFiles) {
                    FileUtil.del(excelLocalFile);
                }
                FileUtil.del(zip);
            }
        });
    }

    private static String getFileNameFromUrl(String url) {
        int index = url.lastIndexOf("/");
        return url.substring(index + 1);
    }

//    public static void main(String[] args) {
//        String[] excelUrls = {
//                "https://cdn.daddylab.com/Upload/supplier/item/1711281366298210304.xlsx",
//                "https://cdn.daddylab.com/Upload/supplier/item/1711277414563393536.xlsx"
//        };
//
//        String localFolderPath = "settlement/123232";
//        String zipFilePath = "settlement_zip/123232.zip";
//        File zipFile = new File(zipFilePath);
//
//        List<File> files = new LinkedList<>();
//        // 下载 Excel 文件到本地
//        for (String url : excelUrls) {
//            String fileName = getFileNameFromUrl(url);
//            File excelLocalFile = new File(localFolderPath + File.separator + fileName);
//            HttpUtil.downloadFileFromUrl(url, excelLocalFile);
//            files.add(excelLocalFile);
//        }
//
//        // 压缩本地文件为一个压缩包
////        FileUtil.mkdir(localFolderPath);
////        File[] files = FileUtil.ls(localFolderPath);
//        File[] array = files.toArray(new File[]{});
//        File zip = ZipUtil.zip(zipFile, false, array);
//        System.out.println(zip.getAbsolutePath());
//    }


    private Boolean afterSalesDetail(String warehouseNo, Long id, ExcelWriter excelWriter) {
        if (StringUtils.isEmpty(warehouseNo)) {
            excelWriter.write(new LinkedList<>(), afterSalesSheet);
            return true;
        }
        List<Long> startDates = iOrderSettlementDetailService.lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, id).select(OrderSettlementDetail::getSettlementStartDate)
                .list().stream().map(OrderSettlementDetail::getSettlementStartDate)
                .distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(startDates)) {
            excelWriter.write(new LinkedList<>(), afterSalesSheet);
            return true;
        }

        return afterSalesDetail0(Arrays.asList(warehouseNo.split(",")), startDates, excelWriter);
    }

    private Boolean afterSalesDetail0(List<String> warehouseNoList, List<Long> durationList, ExcelWriter excelWriter) {
        try {
            AfterSalesRegisterPageQuery pageQuery = new AfterSalesRegisterPageQuery();
            Long[] durations = durationList.toArray(new Long[0]);
            pageQuery.setSettleDurations(durations);
            pageQuery.setWarehouseNos(warehouseNoList);
            pageQuery.setPageSize(99999);
            PageResponse<AfterSalesRegisterPageVO> response = afterSalesRegisterBizService.pageQuery(pageQuery);
            if (response.isSuccess()) {
                List<AfterSalesRegisterPageVO> data = response.getData();
                if (CollUtil.isEmpty(data)) {
                    excelWriter.write(new LinkedList<>(), afterSalesSheet);
                } else {
                    excelWriter.write(data, afterSalesSheet);
                }

                // 针对结算周期2024-03的数据，补充一些上个周期的数据。
//                if (durations.length == 1 && (durations[0] == 1709222400L)) {
//                    List<WarehouseDO> warehouseDOS = iWarehouseService.queryWarehouseMapByNo(warehouseNoList);
//                    if (CollUtil.isNotEmpty(warehouseDOS)) {
//                        List<String> names = warehouseDOS.stream().map(WarehouseDO::getName).collect(Collectors.toList());
//                        List<AfterSalesRegisterPageVO> supplementList =
//                                iTmpAfterSalesSupplementService.lambdaQuery().in(TmpAfterSalesSupplement::getDeliveryWarehouse, names)
//                                        .list().stream().map(this::convert).collect(Collectors.toList());
//                        if (CollUtil.isNotEmpty(supplementList)) {
//                            excelWriter.write(supplementList, afterSalesSheet);
//                        }
//                    }
//                }

                return true;
            } else {
                log.error("exportSettlement.客服售后明细查询失败，导出失败");
                return false;
            }
        } catch (Exception e) {
            log.error("exportSettlement【客服售后明细】sheet导出异常", e);
            return false;
        }
    }

    private AfterSalesRegisterPageVO convert(TmpAfterSalesSupplement tmp) {
        AfterSalesRegisterPageVO vo = new AfterSalesRegisterPageVO();
        vo.setSettleMonth(tmp.getSettlementPeriod());
//        vo.setBusinessLineStr(tmp.getCooperationMode());
        vo.setOrderNo(tmp.getOrderNumber());
        vo.setPayTime(tmp.getPaymentTime());
        vo.setShopName(tmp.getStore());
        vo.setItemCode(tmp.getProductCode());
        vo.setItemName(tmp.getProductName());
        vo.setSpecifications(tmp.getProductSpecification());
        vo.setNum(tmp.getAfterSalesQuantity());
        vo.setWarehouse(tmp.getDeliveryWarehouse());
        vo.setFactoryUndertakeFreight(tmp.getFactoryWarehouseBearsFreight());
        vo.setFactoryUndertakeGoodsAmount(tmp.getFactoryWarehouseBearsPayment());
        vo.setFactoryUndertakeOtherAmount(tmp.getFactoryWarehouseBearsOtherCompensation());
        vo.setExperienceFundCoupon(tmp.getExperienceFundCoupon());
        vo.setExperienceFundCash(tmp.getExperienceFundCash());
        vo.setHandleAdvice(tmp.getAfterSalesHandlingOpinion());
        vo.setExperienceFundReason1(tmp.getExperienceFundBearingReasonOption1());
        vo.setExperienceFundReason2(tmp.getExperienceFundBearingReasonOption2());
        vo.setExperienceFundReason3(tmp.getExperienceFundBearingReasonOption3());
        vo.setDesc1(tmp.getProblemDescriptionOption1());
        vo.setDesc2(tmp.getProblemDescriptionOption2());
        vo.setDesc3(tmp.getProblemDescriptionOption3());
        if (StrUtil.isNotBlank(tmp.getRelatedImages())) {
            vo.setImages(Arrays.asList(tmp.getRelatedImages().split(StrUtil.COMMA)));
        }
        return vo;
    }

    private Boolean deliverDetail(Long formId, String warehouseNo, String relateId, ExcelWriter excelWriter) {
        if (StringUtils.hasText(warehouseNo) && StringUtils.hasText(relateId)) {
            try {
                List<String> skuCodes = iOrderSettlementDetailService.lambdaQuery()
                        .eq(OrderSettlementDetail::getFormId, formId)
                        .select(OrderSettlementDetail::getSkuCode).list()
                        .stream().map(OrderSettlementDetail::getSkuCode).collect(Collectors.toList());

                List<String> warehouseNos = Arrays.asList(warehouseNo.split(","));
                List<Long> relateIds = Arrays.stream(relateId.split(",")).map(Long::valueOf).collect(Collectors.toList());
                List<String> operateTimes = iOrderSettlementFormService.lambdaQuery()
                        .in(OrderSettlementForm::getId, relateIds)
                        .select(OrderSettlementForm::getSettlementStartDate).list()
                        .stream().map(val -> DateUtil.parseTimeStamp(val.getSettlementStartDate(), DatePattern.SIMPLE_MONTH_PATTERN))
                        .distinct().collect(Collectors.toList());
                purchaseBillService.writeStockOutData(operateTimes, skuCodes, warehouseNos, excelWriter);
                return true;
            } catch (Exception e) {
                log.error("exportSettlement.【发货明细】sheet导出异常", e);
                return false;
            }
        }
        return true;
    }

    private Boolean exportRefundMange(Long id, String warehouseNo, ExcelWriter excelWriter) {
        if (!StringUtils.hasText(warehouseNo)) {
            excelWriter.write(new LinkedList<>(), refundDetailSheet);
            return true;
        }
        List<String> warehouseNos = Arrays.asList(warehouseNo.split(","));

        List<OrderSettlementDetail> details = iOrderSettlementDetailService.lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, id).list();
        if (CollUtil.isEmpty(details)) {
            excelWriter.write(new LinkedList<>(), refundDetailSheet);
            return true;
        }
        List<RefundMangerExportCmd> cmdList = details.stream().map(this::convertRefundMangerExportCmd)
                .distinct().collect(Collectors.toList());
        return exportRefundMange0(cmdList, warehouseNos, excelWriter);
    }

    private RefundMangerExportCmd convertRefundMangerExportCmd(OrderSettlementDetail val) {
        RefundMangerExportCmd cmd = new RefundMangerExportCmd();
        cmd.setSkuCode(val.getSkuCode());
        LocalDateTime localDateTime = DateUtil.parseTimeStamp(val.getSettlementStartDate());
        String fs = DateUtil.getFirstDayOfMonth(localDateTime).format(DatePattern.NORM_DATE_FORMATTER);
        String fe = DateUtil.getLastDayOfMonth(localDateTime).format(DatePattern.NORM_DATE_FORMATTER);
        cmd.setCheckStart(fs + " 00:00:00");
        cmd.setCheckEnd(fe + " 23:59:59");
        return cmd;
    }

    private Boolean exportRefundMange0(List<RefundMangerExportCmd> cmdList, List<String> warehouseNos, ExcelWriter excelWriter) {
        try {
            Map<String, List<RefundMangerExportCmd>> collect = cmdList.stream().collect(Collectors.groupingBy(RefundMangerExportCmd::getCheckStart));
            collect.forEach((k, v) -> {
                String checkStart = v.get(0).getCheckStart();
                String checkEnd = v.get(0).getCheckEnd();
                Set<String> paramSkuCodes = v.stream().map(RefundMangerExportCmd::getSkuCode).collect(Collectors.toSet());

                List<RefundManageExportSheet> refundManageExportSheets = wdtRefundOrderMapper.refundManageList3(checkStart, checkEnd, warehouseNos, paramSkuCodes);
                refundManageExportSheets = refundManageExportSheets.stream()
                        .filter(val -> !externalShopWdtNo.getShopNos().contains(val.getShopNo()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(refundManageExportSheets)) {
                    Map<String, String> warehouseNoAndNameMap;
                    if (CollUtil.isEmpty(warehouseNos)) {
                        warehouseNoAndNameMap = Collections.emptyMap();
                    } else {
                        warehouseNoAndNameMap = iWarehouseService.lambdaQuery().in(Warehouse::getNo, warehouseNos).list()
                                .stream().collect(Collectors.toMap(Warehouse::getNo, Warehouse::getName, (a, b) -> a));
                    }
                    refundManageExportSheets.forEach(refundManageExportSheet -> {
                        refundManageExportSheet.setWarehouse(warehouseNoAndNameMap.getOrDefault(refundManageExportSheet.getWarehouseNo(), ""));
                        refundManageExportSheet.setTradeTypeStr(PurchaseBillService.getOrderType(refundManageExportSheet.getTradeType()));
                        refundManageExportSheet.setStatusStr(PurchaseBillService.getRefundStatusStr(refundManageExportSheet.getStatus()));
                    });
                    excelWriter.write(refundManageExportSheets, refundDetailSheet);
                } else {
                    excelWriter.write(Collections.EMPTY_LIST, refundDetailSheet);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("exportSettlement.【退换管理】sheet导出异常", e);
            return false;
        }
    }

    /**
     * [结算单]数据写入
     *
     * @param id
     * @param staticDoStr
     * @param title
     * @param excelWriter
     * @return
     */
    private Boolean exportSettlementOrder(Long id, String staticDoStr, String title, ExcelWriter excelWriter) {
        try {
            List<ExportSettlementOrderDo> excelDos = new LinkedList<>();

            int pageSize = 9999, pageIndex = 1;
            FormDetailPageQuery query = FormDetailPageQuery.buildExportQuery(ListUtil.of(id), pageIndex, pageSize);
            PageResponse<FormDetailPageVo> pageResponse = viewFormDetailPage(query);
            List<FormDetailPageVo> dataList = pageResponse.getData();
            for (int i = 0; i < dataList.size(); i++) {
                FormDetailPageVo val = dataList.get(i);
                ExportSettlementOrderDo excelDo = ExportSettlementOrderDo.ofPageVo(i, val, 2, RoundingMode.HALF_UP);
                excelDo.setSort(String.valueOf(i + 1));
                excelDos.add(excelDo);
            }

            // 填充sku明细部署
            excelWriter.fill(excelDos, fillConfig, settlementSheet);

            // 填充表格名称和汇总等等数据
            Map<String, Object> fillStaticInfo = getExcelFillStaticInfo(staticDoStr, excelDos);
            fillStaticInfo.put("title", title);
            excelWriter.fill(fillStaticInfo, fillConfig, settlementSheet);

            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.setForceFormulaRecalculation(true);
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

            return true;
        } catch (Exception e) {
            log.error("exportSettlement.【结算单】sheet导出异常", e);
            return false;
        }
    }

    /**
     * 自定义 CellWriteHandler 处理器，用于填充公式
     */
    private static class FormulaCellWriteHandler implements CellWriteHandler {
        private final Map<String, Object> data;

        public FormulaCellWriteHandler(Map<String, Object> data) {
            this.data = data;
        }

        @Override
        public void beforeCellCreate(CellWriteHandlerContext context) {
            // 在创建单元格之前，检查是否有需要填充公式的单元格
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            Row row = sheet.getRow(context.getRowIndex());
            Cell cell = row.getCell(context.getColumnIndex());
            String cellAddress = cell.getAddress().formatAsString();

            // 如果需要填充公式，则设置公式
            if (data.containsKey(cellAddress)) {
                Object formula = data.get(cellAddress);
                if (formula instanceof String) {
                    cell.setCellFormula((String) formula);
                }
            }
        }

        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {
            // 在处理完单元格后，无需执行任何操作
        }
    }


    @Override
    public void exportDetailExcel(Long id) {
        OrderSettlementForm form = iOrderSettlementFormService.getById(id);
        Assert.notNull(form, "id非法");
        Assert.isTrue(form.getStatus().equals(OrderSettlementStatus.CONFIRMED), "只有已结算的单据才能导出");
        // 结算供应商
        Long providerId = form.getSProviderId();
        Provider provider = iProviderService.getById(providerId);
        String filePrefix = String.format("老爸评测【%s】订货明细表", provider.getName());

        ExportTask exportTask = ExportTask.initTask(filePrefix, ExportTaskType.SETTLEMENT_DETAIL);
        iExportTaskService.save(exportTask);
        singleExecutor.submit(() -> exportExcelDetailPlus(exportTask, filePrefix, form.getId(), form.getStaticInfo()));
    }

    @Override
    @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER, msg = "单据正在导入数据中，请稍后再试", waitTime = 0, leaseTime = 180)
    public com.alibaba.cola.dto.Response importDetailExcel(InputStream inputStream, @DistributedLockKey Long id) {
        log.info("order settlement import task start. user:{}", UserContext.getNickName());
        EasyExcel.read(inputStream, ImportExcelDo.class, new ReadListener<ImportExcelDo>() {
            public static final int BATCH_COUNT = 9999;
            private final List<OrderSettlementDetail> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            private final List<ImportExcelDo> staticList = ListUtils.newArrayListWithExpectedSize(5);

            @Override
            public void invoke(ImportExcelDo data, AnalysisContext context) {
                try {
                    String sort = data.getSort();
                    if (Objects.isNull(sort)) {
                        return;
                    }
                    if (StrUtil.isNotBlank(sort) && NumberUtil.isNumber(sort)) {
                        if (StrUtil.isBlank(data.getSkuCode())) {
                            return;
                        }
                        OrderSettlementDetail detail = ImportExcelDo.convert(id, data);
                        cachedDataList.add(detail);
                    }
                    if (StrUtil.isNotBlank(sort) && STATIC_FLAGS.contains(sort)) {
                        staticList.add(data);
                    }
                } catch (Exception e) {
                    log.error("结算导入excel文件解析异常", e);
                    throw e;
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                try {
                    String detailStaticDoStr = getDetailStaticDoStr(staticList);
                    orderSettlementDao.saveExcelInfo(id, detailStaticDoStr, cachedDataList);
                } catch (Exception e) {
                    log.error("结算导入excel文件保存处理异常", e);
                    throw e;
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) throws Exception {
                throw exception;
            }

        }).sheet().doRead();

        return com.alibaba.cola.dto.Response.buildSuccess();
    }


    @Override
    public PageResponse<SettlementOrderPageVo> settlementPageQuery(SettlementOrderPageQuery query) {
        Set<Long> formIds = new HashSet<>();
        if (CollUtil.isNotEmpty(query.getTimes())) {
            formIds = iOrderSettlementFormService.lambdaQuery()
                    .ne(OrderSettlementForm::getNo, "")
                    .isNotNull(OrderSettlementForm::getNo)
                    .in(OrderSettlementForm::getSettlementStartDate, query.getTimes())
                    .select(OrderSettlementForm::getId)
                    .list().stream().map(OrderSettlementForm::getId)
                    .collect(Collectors.toSet());
            if (CollUtil.isEmpty(formIds)) {
                return ResponseFactory.emptyPage();
            }
        }
        List<String> queryWarehouseNos;
        if (CollUtil.isNotEmpty(query.getOrderPersonnelIds())) {
            queryWarehouseNos = iWarehouseService.getWarehouseNoByOrderPersonnel(query.getOrderPersonnelIds());
            if (CollUtil.isEmpty(queryWarehouseNos)) {
                return ResponseFactory.emptyPage();
            }
            if (CollUtil.isNotEmpty(query.getWarehouseNos())) {
                Collection<String> intersection = CollectionUtil.intersection(query.getWarehouseNos(), queryWarehouseNos);
                if (CollUtil.isEmpty(intersection)) {
                    return ResponseFactory.emptyPage();
                }
                queryWarehouseNos = (List<String>) intersection;
            }
        } else {
            if (Objects.isNull(query.getWarehouseNos())) {
                queryWarehouseNos = new ArrayList<>();
            } else {
                queryWarehouseNos = new ArrayList<>(query.getWarehouseNos());
            }
        }
        // 付款状态
        List<Long> matchedRelatedIdList = new LinkedList<>();
        List<PaymentOrderStatus> paymentOrderStatusList = query.getPaymentOrderStatusList();
        if (Objects.nonNull(query.getPaymentOrderStatus())) {
            paymentOrderStatusList.add(query.getPaymentOrderStatus());
        }
        if (CollUtil.isNotEmpty(paymentOrderStatusList)) {
            matchedRelatedIdList = iPaymentApplyOrderService.getMatchedRelatedIdByStatus(paymentOrderStatusList, 1);
            if (CollUtil.isEmpty(matchedRelatedIdList)) {
                return ResponseFactory.emptyPage();
            }
        }
//        if (Objects.nonNull(query.getPaymentOrderStatus())) {
//                matchedRelatedIdList = iPaymentApplyOrderService.getMatchedRelatedIdByStatus(ListUtil.of(query.getPaymentOrderStatus()), 1);
//                if (CollUtil.isEmpty(matchedRelatedIdList)) {
//                    return ResponseFactory.emptyPage();
//                }
//            } else {
//                matchedRelatedIdList = new LinkedList<>();
//        }
        // 付款申请状态处理
        List<Long> chooseByApplyPayStatusIdList;
        PayApplyStatus payApplyStatus = query.getPayApplyStatus();
        if (Objects.nonNull(payApplyStatus)) {
            if (PayApplyStatus.WAIT.equals(payApplyStatus)) {
                chooseByApplyPayStatusIdList = orderSettlementFormMapper.getNoPayAndWaitPayFormId();
            } else if (PayApplyStatus.APPLYING.equals(payApplyStatus)) {
                chooseByApplyPayStatusIdList = orderSettlementFormMapper.getApplyingPayFormId();
            } else if (PayApplyStatus.FINISHED.equals(payApplyStatus)) {
                chooseByApplyPayStatusIdList = orderSettlementFormMapper.getFinishPayFormId();
            } else {
                chooseByApplyPayStatusIdList = new LinkedList<>();
            }
            if (CollUtil.isEmpty(chooseByApplyPayStatusIdList)) {
                return ResponseFactory.emptyPage();
            }
        } else {
            chooseByApplyPayStatusIdList = null;
        }

        List<Long> idFilterIds = new LinkedList<>();
        List<Collection<Long>> idCollectionList = ListUtil.of(formIds, chooseByApplyPayStatusIdList, matchedRelatedIdList)
                .stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(idCollectionList)) {
            if (idCollectionList.size() == 1) {
                idFilterIds.addAll(idCollectionList.get(0));
            }
            if (idCollectionList.size() == 2) {
                Collection<Long> intersection = CollectionUtil.intersection(idCollectionList.get(0), idCollectionList.get(1));
                idFilterIds.addAll(intersection);
            }
            if (idCollectionList.size() == 3) {
                Collection<Long> intersection = CollectionUtil.intersection(idCollectionList.get(0), idCollectionList.get(1));
                Collection<Long> intersection1 = CollectionUtil.intersection(intersection, idCollectionList.get(2));
                idFilterIds.addAll(intersection1);
            }
        }

        List<String> finalQueryWarehouseNos = queryWarehouseNos;
        PageInfo<OrderSettlementForm> page = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> {
                            iOrderSettlementFormService.lambdaQuery()
                                    .eq(StringUtils.hasText(query.getNo()), OrderSettlementForm::getNo, query.getNo())
                                    .eq(Objects.nonNull(query.getProviderId()), OrderSettlementForm::getPProviderId, query.getProviderId())
                                    .ne(OrderSettlementForm::getNo, "").isNotNull(OrderSettlementForm::getNo)
                                    .in(CollUtil.isNotEmpty(query.getBusinessLine()), OrderSettlementForm::getBusinessLine, query.getBusinessLine())
                                    .and(CollUtil.isNotEmpty(finalQueryWarehouseNos), ww -> {
                                        for (int i = 0; i < finalQueryWarehouseNos.size(); i++) {
                                            ww.like(OrderSettlementForm::getWarehouseNo, finalQueryWarehouseNos.get(i));
                                            if (i != finalQueryWarehouseNos.size() - 1) {
                                                ww.or();
                                            }
                                        }
                                    })
                                    .in(CollUtil.isNotEmpty(idFilterIds), OrderSettlementForm::getId, idFilterIds)
                                    .orderByDesc(OrderSettlementForm::getId).list();
                        }
                );
        List<OrderSettlementForm> result = page.getList();
        if (CollUtil.isEmpty(result)) {
            return PageResponse.of(new ArrayList<>(), (int) page.getTotal(), query.getPageSize(), query.getPageIndex());
        }

        Set<Long> providerIds = result.stream().map(OrderSettlementForm::getPProviderId).collect(Collectors.toSet());
        final List<Provider> providers = iProviderService.lambdaQuery().in(Provider::getId, providerIds).list();
        Map<Long, Provider> providerMap = providers
                .stream().collect(Collectors.toMap(Provider::getId, Function.identity()));

        final Set<Long> partnerProviderIds = providers.stream().map(Provider::getPartnerProviderId).collect(Collectors.toSet());
        final Map<Long, PartnerProviderResp> providerRespMap = providerGateway.partnerBatchQueryByIds(partnerProviderIds);

        List<String> warehouseNos = result.stream().map(val -> {
            String warehouseNo = val.getWarehouseNo();
            if (StringUtils.hasText(warehouseNo)) {
                return Arrays.asList(warehouseNo.split(","));
            }
            return new ArrayList<String>();
        }).flatMap(List::stream).distinct().collect(Collectors.toList());
        Map<String, String> warehouseMap = iWarehouseService.getNameAndNo(warehouseNos);
        Map<String, List<String>> orderPersonnelNickName = iWarehouseService.getOrderPersonnelNickName(warehouseNos);

        Set<Long> resultFormIds = result.stream().map(OrderSettlementForm::getId).collect(Collectors.toSet());
        List<DetailQuantityStaticDo> quantityStaticList = orderSettlementDetailMapper.getQuantityStatic(resultFormIds);
        Map<Long, DetailQuantityStaticDo> quantityStaticMap = quantityStaticList.stream()
                .collect(Collectors.toMap(DetailQuantityStaticDo::getFormId, v -> v));
        Map<Long, List<LocalDateTime>> sortDetailTimeMap = getSortTimeMap(resultFormIds);

        return PageResponse.of(result.stream().
                map(val ->
                {
                    SettlementOrderPageVo vo = new SettlementOrderPageVo();
                    vo.setId(val.getId());
                    vo.setNo(val.getNo());
                    vo.setCycle(DateUtil.polymerizationTime(sortDetailTimeMap.getOrDefault(val.getId(), new ArrayList<>())));
                    vo.setProviderId(val.getPProviderId());
                    final Optional<Provider> provider = Optional.ofNullable(providerMap.get(val.getPProviderId()));
                    vo.setProviderName(provider.map(Provider::getName).orElse(""));
                    vo.setIsBlacklist(provider.map(Provider::getPartnerProviderId)
                            .map(providerRespMap::get)
                            .map(PartnerProviderResp::getIsBlacklist)
                            .orElse(0));

                    vo.setWarehouseNo(val.getWarehouseNo());
                    vo.setBusinessLine(val.getBusinessLine());
                    vo.setPayApplyStatus(getPayApplyStatus(val.getNo()));

                    String warehouseNames = "";
                    String orderPersonnel = "";
                    if (StringUtils.hasText(val.getWarehouseNo())) {
                        List<String> oneWarehouseNos = Arrays.asList(val.getWarehouseNo().split(","));
                        warehouseNames = oneWarehouseNos.stream().map(v1 -> warehouseMap.getOrDefault(v1, "未知仓库"))
                                .collect(Collectors.joining(","));
                        orderPersonnel = oneWarehouseNos.stream().map(v2 -> orderPersonnelNickName.getOrDefault(v2, new ArrayList<>()))
                                .flatMap(List::stream).distinct().collect(Collectors.joining(","));
                    }
                    vo.setWarehouseName(warehouseNames);
                    vo.setOrderPersonnel(orderPersonnel);
                    DetailQuantityStaticDo detailQuantityStaticDo = quantityStaticMap.get(val.getId());
                    if (Objects.nonNull(detailQuantityStaticDo)) {
                        vo.setTemporaryQuantity(detailQuantityStaticDo.getTemporaryQuantitySum());
                        vo.setSettlementQuantity(detailQuantityStaticDo.getSettlementQuantitySum());
                    }
                    vo.setVersion(Objects.isNull(val.getVersion()) ? 1 : val.getVersion());
                    return vo;
                }).
                collect(Collectors.toList()), (int) page.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    public PayApplyStatus getPayApplyStatus(String no) {
        List<PaymentApplyStatusDto> list = paymentApplyOrderDetailMapper.getPaymentStatusBySettlementNo(no);
        if (CollUtil.isEmpty(list)) {
            return PayApplyStatus.WAIT;
        }
        long count = list.stream().filter(val -> val.getPaymentStatus().equals(PaymentOrderStatus.WAIT_SUBMIT.getValue())).count();
        if (list.size() == count) {
            return PayApplyStatus.WAIT;
        }

        long count1 = list.stream().filter(val -> val.getPaymentStatus().equals(PaymentOrderStatus.FINISH.getValue())).count();
        if (list.size() == count1) {
            return PayApplyStatus.FINISHED;
        }

        return PayApplyStatus.APPLYING;
    }

    private Map<Long, List<LocalDateTime>> getSortTimeMap(Collection<Long> formIds) {
        return iOrderSettlementDetailService.lambdaQuery()
                .in(OrderSettlementDetail::getFormId, formIds)
                .select(OrderSettlementDetail::getSettlementStartDate, OrderSettlementDetail::getFormId)
                .list()
                .stream().collect(Collectors.groupingBy(
                        OrderSettlementDetail::getFormId,
                        Collectors.mapping(o -> DateUtil.parseTimeStamp(o.getSettlementStartDate()), Collectors.toList())));
    }

    private String getDetailStaticDoStr(List<ImportExcelDo> details) {
        DetailStaticDo detailStaticDo = new DetailStaticDo();

        Optional<ImportExcelDo> tsOpt = details.stream().filter(val -> "小计".equals(val.getSort().trim())).findFirst();
        if (tsOpt.isPresent()) {
            ImportExcelDo detail1 = tsOpt.get();
            detailStaticDo.setTsDeliverQuantity(StrUtil.isBlank(detail1.getDeliverQuantity()) ? 0 : convertInt(detail1.getDeliverQuantity()));
            detailStaticDo.setTsCurrentMonthRefundQuantity(StrUtil.isBlank(detail1.getCurrentMonthRefundQuantity()) ? 0 : convertInt(detail1.getCurrentMonthRefundQuantity()));
            detailStaticDo.setTsCrossMonthRefundQuantity(StrUtil.isBlank(detail1.getCrossMonthRefundQuantity()) ? 0 : convertInt(detail1.getCrossMonthRefundQuantity()));
            detailStaticDo.setTsSettlementQuantity(StrUtil.isBlank(detail1.getSettlementQuantity()) ? 0 : convertInt(detail1.getSettlementQuantity()));
            detailStaticDo.setTsSettlementAmount(StrUtil.isBlank(detail1.getSettlementAmount()) ? BigDecimal.ZERO :
                    new BigDecimal(detail1.getSettlementAmount()));
            detailStaticDo.setTsFinalAmount(StrUtil.isBlank(detail1.getFinalAmount()) ? BigDecimal.ZERO :
                    new BigDecimal(detail1.getFinalAmount()));
            detailStaticDo.setTsTransportAndAfterSalesCost(StrUtil.isBlank(detail1.getTransportAndAfterSalesCost()) ? BigDecimal.ZERO :
                    new BigDecimal(detail1.getTransportAndAfterSalesCost()));
            detailStaticDo.setTsRemark(detail1.getRemark());
        }

        Optional<ImportExcelDo> transportOpt = details.stream().filter(val -> "运费".equals(val.getSort().trim())).findFirst();
        if (transportOpt.isPresent()) {
            ImportExcelDo detail2 = transportOpt.get();
            detailStaticDo.setTransportAmount(StrUtil.isBlank(detail2.getSettlementAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail2.getSettlementAmount()) ? new BigDecimal(detail2.getSettlementAmount()) : BigDecimal.ZERO);
            detailStaticDo.setTransportFinalAmount(StrUtil.isBlank(detail2.getFinalAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail2.getFinalAmount()) ? new BigDecimal(detail2.getFinalAmount()) : BigDecimal.ZERO);
            detailStaticDo.setTransportRemark(detail2.getRemark());
        }

        Optional<ImportExcelDo> afterSalesOpt = details.stream().filter(val -> "售后".equals(val.getSort().trim())).findFirst();
        if (afterSalesOpt.isPresent()) {
            ImportExcelDo detail3 = afterSalesOpt.get();
            detailStaticDo.setAfterSalesAmount(StrUtil.isBlank(detail3.getSettlementAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail3.getSettlementAmount()) ? new BigDecimal(detail3.getSettlementAmount()) : BigDecimal.ZERO);
            detailStaticDo.setAfterSalesFinalAmount(StrUtil.isBlank(detail3.getFinalAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail3.getFinalAmount()) ? new BigDecimal(detail3.getFinalAmount()) : BigDecimal.ZERO);
            detailStaticDo.setAfterSalesRemark(detail3.getRemark());
        }

        Optional<ImportExcelDo> otherOpt = details.stream().filter(val -> "其他".equals(val.getSort().trim())).findFirst();
        if (otherOpt.isPresent()) {
            ImportExcelDo detail4 = otherOpt.get();
            detailStaticDo.setOtherAmount(StrUtil.isBlank(detail4.getSettlementAmount()) ? BigDecimal.ZERO :
                    (NumberUtil.isNumber(detail4.getSettlementAmount()) ? new BigDecimal(detail4.getSettlementAmount()) : BigDecimal.ZERO));
            detailStaticDo.setOtherFinalAmount(StrUtil.isBlank(detail4.getFinalAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail4.getFinalAmount()) ? new BigDecimal(detail4.getFinalAmount()) : BigDecimal.ZERO);
            detailStaticDo.setOtherRemark(detail4.getRemark());
        }

        Optional<ImportExcelDo> totalOpt = details.stream().filter(val -> "合计".equals(val.getSort().trim())).findFirst();
        if (totalOpt.isPresent()) {
            ImportExcelDo detail5 = totalOpt.get();
            detailStaticDo.setTotalSettlementQuantity(StrUtil.isBlank(detail5.getSettlementQuantity()) ? 0 : convertInt(detail5.getSettlementQuantity()));
            detailStaticDo.setTotalSettlementAmount(StrUtil.isBlank(detail5.getSettlementAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail5.getSettlementAmount()) ? new BigDecimal(detail5.getSettlementAmount()) : BigDecimal.ZERO);
            detailStaticDo.setTotalFinalAmount(StrUtil.isBlank(detail5.getFinalAmount()) ? BigDecimal.ZERO :
                    NumberUtil.isNumber(detail5.getFinalAmount()) ? new BigDecimal(detail5.getFinalAmount()) : BigDecimal.ZERO);
            detailStaticDo.setTotalRemark(detail5.getRemark());
        }

        return JsonUtil.toJson(detailStaticDo);
    }

    private void exportExcelDetailPlus(ExportTask exportTask, String filePrefix, Long id, String staticDoStr) {
        File dataFile = new File(filePrefix + "_" + RandomUtil.randomString(4) + ".xlsx");
        File exportExcelTemplate = getExportExcelTemplate(SETTLEMENT_EXCEL_NAME);
        ExcelWriter excelWriter = EasyExcelFactory.write(dataFile)
                .withTemplate(exportExcelTemplate.getAbsolutePath())
                .inMemory(true).build();
        boolean exportSuccess = exportSettlementOrder(id, staticDoStr, filePrefix, excelWriter);

        if (!exportSuccess) {
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError("导出失败");
        } else {
            try {
                excelWriter.finish();

                UploadFileAction action = UploadFileAction.ofFile(dataFile);
                String url = fileGateway.uploadFile(action).getUrl();
                exportTask.setDownloadUrl(url);
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
            } catch (Exception e) {
                log.error("采购结算单列表导出数据上传又拍云失败", e);
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
            } finally {
                FileUtil.del(dataFile);
            }
        }

        iExportTaskService.updateById(exportTask);
    }

    /**
     * 获取 采购订单-导出模板。
     *
     * @return 模板文件
     */
    private File getExportExcelTemplate(String modelName) {
        // 获取检查最新的导出模板数据
        List<Additional> list = iAdditionalService.lambdaQuery().eq(Additional::getName, modelName + ".xlsx")
                .orderByDesc(Additional::getId).last("limit 1").list();
        Assert.isTrue(CollUtil.isNotEmpty(list), modelName + "。此模板不存在，请检查附件列表");
        Additional additional = list.get(0);
        File modelFile = new File(modelName + "_" + additional.getId() + ".xlsx");
        if (modelFile.exists()) {
            return modelFile;
        }

        String modelDownloadUrl = additional.getDownloadUrl();
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(modelDownloadUrl)
                .build();
        try (Response response = client.newCall(request).execute()) {
            ResponseBody body = response.body();
            if (body != null) {
                FileOutputStream fos = new FileOutputStream(modelFile);
                fos.write(body.bytes());
                fos.close();
            } else {
                throw new RuntimeException("Failed to download Excel file");
            }
        } catch (IOException e) {
            log.error("failed to download Excel file", e);
            throw new RuntimeException("Failed to download Excel file");
        }

        return modelFile;
    }

    private Map<String, Object> getFillStaticInfo(String staticDoStr) {
        DetailStaticDo staticDo = new DetailStaticDo();
        if (StringUtils.hasText(staticDoStr)) {
            staticDo = JsonUtil.parse(staticDoStr, DetailStaticDo.class);
            if (Objects.isNull(staticDo)) {
                staticDo = new DetailStaticDo();
            }
        }
        Map<String, Object> map = buildExcelFillMap(staticDo);
        map.put("tsSettlementAmount", staticDo.getTsSettlementAmount());
        map.put("tsFinalAmount", staticDo.getTsFinalAmount());
        map.put("transportAmount", staticDo.getTransportAmount());
        map.put("afterSalesAmount", staticDo.getAfterSalesAmount());
        map.put("otherAmount", staticDo.getOtherAmount());
        map.put("totalSettlementQuantity", staticDo.getTotalSettlementQuantity());
        map.put("totalSettlementAmount", staticDo.getTotalSettlementAmount());
        map.put("totalFinalAmount", staticDo.getTotalFinalAmount());
        return map;
    }

    private Map<String, Object> getExcelFillStaticInfo(String staticDoStr, List<ExportSettlementOrderDo> excelDos) {
        if (!StringUtils.hasText(staticDoStr)) {
            return new HashMap<>(16);
        }
        if (CollUtil.isEmpty(excelDos)) {
            return new HashMap<>(16);
        }
        DetailStaticDo staticDo = JsonUtil.parse(staticDoStr, DetailStaticDo.class);
        if (Objects.isNull(staticDo)) {
            return new HashMap<>(16);
        }

//        Map<String, Object> map = buildExcelFillMap(staticDo);
        Map<String, Object> map = new HashMap<>(16);

        // 结算数量 合计
        FormulaData totalSettlementQuantity = new FormulaData();
        totalSettlementQuantity.setFormulaValue(StrUtil.format("SUM(K3:K{})", excelDos.size() + 2));
        WriteCellData<String> totalSettlementQuantityCellData = new WriteCellData<>();
        totalSettlementQuantityCellData.setFormulaData(totalSettlementQuantity);
        map.put("totalSettlementQuantity", totalSettlementQuantityCellData);
        // 结算数量 小记
        map.put("tsSettlementQuantity", totalSettlementQuantityCellData);

        // 发货数量 小记
        FormulaData tsDeliverQuantity = new FormulaData();
        tsDeliverQuantity.setFormulaValue(StrUtil.format("SUM(H3:H{})", excelDos.size() + 2));
        WriteCellData<Integer> tsDeliverQuantityCellData = new WriteCellData<>();
        tsDeliverQuantityCellData.setFormulaData(tsDeliverQuantity);
        map.put("tsDeliverQuantity", tsDeliverQuantityCellData);

        // 当月退货 小记
        FormulaData tsCurrentMonthRefundQuantity = new FormulaData();
        tsCurrentMonthRefundQuantity.setFormulaValue(StrUtil.format("SUM(I3:I{})", excelDos.size() + 2));
        WriteCellData<Integer> tsCurrentMonthRefundQuantityCellData = new WriteCellData<>();
        tsCurrentMonthRefundQuantityCellData.setFormulaData(tsCurrentMonthRefundQuantity);
        map.put("tsCurrentMonthRefundQuantity", tsCurrentMonthRefundQuantityCellData);

        // 跨月退货 小记
        FormulaData tsCrossMonthRefundQuantity = new FormulaData();
        tsCrossMonthRefundQuantity.setFormulaValue(StrUtil.format("SUM(J3:J{})", excelDos.size() + 2));
        WriteCellData<Integer> tsCrossMonthRefundQuantityCellData = new WriteCellData<>();
        tsCrossMonthRefundQuantityCellData.setFormulaData(tsCrossMonthRefundQuantity);
        map.put("tsCrossMonthRefundQuantity", tsCrossMonthRefundQuantityCellData);


        // 结算金额 小记
        FormulaData tsSettlementAmount = new FormulaData();
        tsSettlementAmount.setFormulaValue(StrUtil.format("SUM(L3:L{})", excelDos.size() + 2));
        WriteCellData<String> tsSettlementAmountCellData = new WriteCellData<>();
        tsSettlementAmountCellData.setFormulaData(tsSettlementAmount);
        map.put("tsSettlementAmount", tsSettlementAmountCellData);
        // 最终金额 小记
        FormulaData tsFinalAmount = new FormulaData();
        tsFinalAmount.setFormulaValue(StrUtil.format("SUM(N3:N{})", excelDos.size() + 2));
        WriteCellData<String> tsFinalAmountCellData = new WriteCellData<>();
        tsFinalAmountCellData.setFormulaData(tsFinalAmount);
        map.put("tsFinalAmount", tsFinalAmountCellData);
        // 运费及售后分摊 小记
        FormulaData tsAfterSalesCost = new FormulaData();
        tsAfterSalesCost.setFormulaValue(StrUtil.format("SUM(M3:M{})", excelDos.size() + 2));
        WriteCellData<String> tsAfterSalesCostCellData = new WriteCellData<>();
        tsAfterSalesCostCellData.setFormulaData(tsAfterSalesCost);
        map.put("tsAfterSalesCost", tsAfterSalesCostCellData);

        // 运费
        BigDecimal transportAmount = Objects.isNull(staticDo.getTransportAmount()) ? BigDecimal.ZERO :
                staticDo.getTransportAmount().setScale(2, RoundingMode.HALF_UP);
        map.put("transportAmount", transportAmount);
        // 售后
        BigDecimal afterSalesAmount = Objects.isNull(staticDo.getAfterSalesAmount()) ? BigDecimal.ZERO :
                staticDo.getAfterSalesAmount().setScale(2, RoundingMode.HALF_UP);
        map.put("afterSalesAmount", afterSalesAmount);
        // 其他
        BigDecimal otherAmount = Objects.isNull(staticDo.getOtherAmount()) ? BigDecimal.ZERO :
                staticDo.getOtherAmount().setScale(2, RoundingMode.HALF_UP);
        map.put("otherAmount", otherAmount);

        // 合计 结算金额
        FormulaData totalSettlementAmount = new FormulaData();
        totalSettlementAmount.setFormulaValue(StrUtil.format("L{}+L{}+L{}+L{}",
                excelDos.size() + 3, excelDos.size() + 4, excelDos.size() + 5, excelDos.size() + 6));
        WriteCellData<String> totalSettlementAmountCellData = new WriteCellData<>();
        totalSettlementAmountCellData.setFormulaData(totalSettlementAmount);
        map.put("totalSettlementAmount", totalSettlementAmountCellData);
        // 合计 最终金额 = 合计 结算金额
        map.put("totalFinalAmount", totalSettlementAmountCellData);

        return map;
    }

    private Map<String, Object> buildExcelFillMap(DetailStaticDo staticDo) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("tsDeliverQuantity", staticDo.getTsDeliverQuantity());
        map.put("tsCurrentMonthRefundQuantity", staticDo.getTsCurrentMonthRefundQuantity());
        map.put("tsCrossMonthRefundQuantity", staticDo.getTsCrossMonthRefundQuantity());
        map.put("tsSettlementQuantity", staticDo.getTsSettlementQuantity());
        return map;
    }


    // ---------------------------------------------------------------------------


    @Override
    public void exportRefundInfo(Long settlementDate) {
        List<OrderSettlementForm> list = iOrderSettlementFormService.lambdaQuery()
                .eq(OrderSettlementForm::getSettlementStartDate, settlementDate)
                .isNotNull(OrderSettlementForm::getNo).ne(OrderSettlementForm::getNo, "")
                .eq(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
                .list();
        log.info("退换管理导出总数.size:{}", list.size());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            File file = new File(RandomUtil.randomString(6) + ".xlsx");
            ExcelWriter excelWriter = EasyExcelFactory.write(file).build();
            AtomicInteger count = new AtomicInteger();
            list.forEach(form -> {
                boolean bb = exportRefundMange(form.getId(), form.getWarehouseNo(), excelWriter);
                if (!bb) {
                    log.info("退换管理导出异常.no:{}", form.getNo());
                } else {
                    count.getAndIncrement();
                    log.info("退换管理导出成功.no:{},累计:{}", form.getNo(), count.get());
                }
            });
            excelWriter.finish();

            UploadFileAction action = UploadFileAction.ofFile(file);
            FileStub fileStub = fileGateway.uploadFile(action);
            com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File sysFile = iFileService.getById(fileStub.getFileId());
            String url = ossGateway.generatePresignedUrl(true, sysFile.getPath());
            String format = StrUtil.format("采购结算退换明细汇总。结算周期:{},下载url:{}"
                    , DateUtil.parseTimeStamp(settlementDate, DatePattern.NORM_DATE_PATTERN), url);
            Alert.text(MessageRobotCode.GLOBAL, format);
            log.info("退换管理导出Excel下载地址:{}", url);
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderForm(List<String> nos) {
        List<OrderSettlementForm> list = iOrderSettlementFormService.lambdaQuery().in(OrderSettlementForm::getNo, nos).list();
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 回滚结算单据关联的系统结算单状态。已结算->待结算
        Set<Long> sysOrderIds = new HashSet<>();
        list.forEach(val -> {
            String relateId = val.getRelateId();
            if (StrUtil.isNotBlank(relateId)) {
                Set<Long> collect1 = Arrays.stream(relateId.split(StrUtil.COMMA)).map(Long::valueOf).collect(Collectors.toSet());
                sysOrderIds.addAll(collect1);
            }
        });
        if (CollUtil.isNotEmpty(sysOrderIds)) {
            sysOrderIds.forEach(sysOrderId -> {
                // 检查这笔系统结算单据是否关联了删除名单之外的业务结算单据，如果关联了，状态不变。
                Integer relateOtherOrderCount = iOrderSettlementFormService.lambdaQuery()
                        .like(OrderSettlementForm::getRelateId, sysOrderId)
                        .ne(OrderSettlementForm::getNo, StrUtil.EMPTY)
                        .isNotNull(OrderSettlementForm::getNo)
                        .notIn(OrderSettlementForm::getNo, nos)
                        .count();
                if (relateOtherOrderCount == 0) {
                    iOrderSettlementFormService.lambdaUpdate()
                            .set(OrderSettlementForm::getStatus, OrderSettlementStatus.WAIT_CONFIRM)
                            .eq(OrderSettlementForm::getId, sysOrderId)
                            .update();
                }
            });
        }

        List<Long> formIds = list.stream().map(OrderSettlementForm::getId).collect(Collectors.toList());
        iOrderSettlementDetailService.lambdaUpdate().in(OrderSettlementDetail::getFormId, formIds).remove();
        iOrderSettlementFormService.lambdaUpdate().in(OrderSettlementForm::getNo, nos).remove();
    }

    @Override
    public com.alibaba.cola.dto.SingleResponse<PaymentDetailAmountVO> getPayAmount(Long id, List<Long> choosedDetailIdList) {
        OrderSettlementForm orderSettlementForm = iOrderSettlementFormService.getById(id);
        Assert.notNull(orderSettlementForm, "此id非法，查询不到结算单");

        // 结算单总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        String staticInfo = orderSettlementForm.getStaticInfo();
        if (StrUtil.isNotBlank(staticInfo)) {
            DetailStaticDo detailStaticDo;
            try {
                detailStaticDo = JsonUtil.parse(staticInfo, DetailStaticDo.class);
                if (Objects.nonNull(detailStaticDo)) {
                    totalAmount = detailStaticDo.getTotalFinalAmount();
                }
            } catch (Exception e) {
                log.error("获取结算单合计金额失败.id:{}", id, e);
                throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "获取结算单合计金额失败");
            }
        }

        // 已申请付款金额
        BigDecimal appliedPaymentAmount = iPaymentApplyOrderService.getAppliedPaymentAmountByRelatedNo(ListUtil.of(orderSettlementForm.getNo()));

        // 剩余可申请付款金额
        BigDecimal remainingPayAmount = totalAmount.subtract(appliedPaymentAmount);
        if (remainingPayAmount.compareTo(BigDecimal.ZERO) == 0) {
            String tip = StrUtil.format("此结算单可付款金额总计:{},已申请付款金额:{},剩余可付款金额:{},剩余可付款金额不得为0",
                    totalAmount.setScale(2, RoundingMode.HALF_UP), appliedPaymentAmount.setScale(2, RoundingMode.HALF_UP), remainingPayAmount.setScale(2, RoundingMode.HALF_UP));
            return com.alibaba.cola.dto.SingleResponse.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), tip);
        }
        // 应付金额
        BigDecimal rightAmount = totalAmount.subtract(appliedPaymentAmount);

        // 针对部分SKU部分明细的情况
        if (CollUtil.isNotEmpty(choosedDetailIdList)) {
            List<OrderSettlementDetail> detailList = iOrderSettlementDetailService.lambdaQuery()
                    .in(OrderSettlementDetail::getId, choosedDetailIdList).list();
            BigDecimal chooseRightPayAmount = detailList.stream().map(OrderSettlementDetail::getFinalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            rightAmount = chooseRightPayAmount.subtract(appliedPaymentAmount);

            totalAmount = iOrderSettlementDetailService.lambdaQuery()
                    .eq(OrderSettlementDetail::getFormId, id)
                    .in(OrderSettlementDetail::getId, choosedDetailIdList)
                    .select(OrderSettlementDetail::getFinalAmount).list()
                    .stream().map(OrderSettlementDetail::getFinalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        PaymentDetailAmountVO vv = new PaymentDetailAmountVO();
        vv.setRightAmount(rightAmount.setScale(2, RoundingMode.HALF_UP));
        vv.setOrderAmount(totalAmount.setScale(2, RoundingMode.HALF_UP));
        return SingleResponse.of(vv);
    }

    @Override
    @DistributedLock
    public SingleResponse<String> deleteApply(Long id) {
        deleteCheck(id);
        List<Long> longs = paymentApplyOrderDetailMapper.getWaitSubmitPaymentApplyOrderIdBySettlementOrderId(id);
        if (CollUtil.isNotEmpty(longs)) {
            return SingleResponse.of("当前结算单存在【待提交】的付款单，点击“确认”后结算单和付款单都会删除，且不可恢复！！");
        } else {
            orderSettlementDao.remove(id);
            return SingleResponse.of(StrUtil.EMPTY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> confirmDelete(Long id) {
        deleteCheck(id);
        List<Long> longs = paymentApplyOrderDetailMapper.getWaitSubmitPaymentApplyOrderIdBySettlementOrderId(id);
        if (CollUtil.isNotEmpty(longs)) {
            paymentOrderBizService.deleteByFormIds(longs);
        }
        orderSettlementDao.remove(id);
        return SingleResponse.of(true);
    }

    private void deleteCheck(Long id) {
        OrderSettlementForm form = iOrderSettlementFormService.getById(id);
        Assert.notNull(form, "结算单ID非法");
        boolean b = UserContext.hasPermission(GlobalConstant.SETTLEMENT_ORDER_DEL_API, ResourceType.API);
        Assert.isTrue(b, "没有删除权限，不允许操作");
        boolean admin = UserPermissionJudge.isAdmin();
        if (!admin) {
            Assert.isTrue(form.getCreatedUid().equals(UserContext.getUserId()), "非管理员，只允许删除自己创建的单据");
        }
    }

    @Override
    public SingleResponse<String> refreshSettlementDate(List<Long> remainDetailIds) {
        if (CollUtil.isEmpty(remainDetailIds)) {
            return SingleResponse.of(StrUtil.EMPTY);
        }
        List<LocalDateTime> collect = iOrderSettlementDetailService.lambdaQuery()
                .in(OrderSettlementDetail::getId, remainDetailIds)
                .list().stream()
                .map(val -> DateUtil.parseTimeStamp(val.getSettlementStartDate()))
                .collect(Collectors.toList());
        return SingleResponse.of(DateUtil.polymerizationTime(collect));
    }


}

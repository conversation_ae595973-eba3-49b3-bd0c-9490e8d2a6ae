package com.daddylab.supplier.item.domain.item.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailBaseVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailImageVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailProcurementVo;
import com.daddylab.supplier.item.controller.item.dto.detail.SonItemVO;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 10:07 上午
 * @description
 */
public class ItemViewEntity {

    public ItemDetailBaseVo getBase(Long itemId, ItemGateway itemGateway, UserGateway userGateway, ProviderGateway providerGateway) {
        final ItemBaseDO baseInfo = itemGateway.getBaseInfo(itemId);
        final ItemDetailBaseVo itemDetailBaseVo = ItemTransMapper.INSTANCE.baseDoToDetailVo(baseInfo);

        final IBizLevelDivisionService bizLevelDivisionService = SpringUtil.getBean(IBizLevelDivisionService.class);
        final List<CorpBizTypeDTO> corpBizType1 = bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemId);
        itemDetailBaseVo.setCorpBizType(corpBizType1);

        itemDetailBaseVo.setIsBlacklist(
                Optional.ofNullable(baseInfo.getPartnerProviderId())
                        .filter(NumberUtil::isPositive)
                        .map(providerGateway::partnerQueryById)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .map(PartnerProviderResp::getIsBlacklist)
                        .orElse(0));

        if (StringUtil.isNotBlank(itemDetailBaseVo.getBuyerUserId())) {
            final long buyerUserId = Long.parseLong(itemDetailBaseVo.getBuyerUserId());
            final StaffInfo staffInfo = userGateway.queryStaffInfoById(buyerUserId);
            if (staffInfo != null) {
                itemDetailBaseVo.setBuyerNickName(staffInfo.getNickname());
            }
        }

        List<SonItemVO> list = new LinkedList<>();
        final List<Item> sonItem = itemGateway.getSonItem(itemId);
        if (Objects.nonNull(sonItem)) {
            for (Item item : sonItem) {
                SonItemVO vo = new SonItemVO();
                vo.setItemCode(item.getCode());
                vo.setItemId(item.getId());
                list.add(vo);
            }
            itemDetailBaseVo.setSonItemList(list);
        }
        return itemDetailBaseVo;
    }

    /**
     * 查询商品sku信息
     *
     * @return sku信息前端返回封装
     */
    public List<ItemSkuListDto> getSkuList(Long itemId, ItemGateway itemGateway, WarehouseStockGateway warehouseStockGateway) {
        final List<ItemSku> skuList = itemGateway.getSkuList(itemId);
        final List<Long> skuIdList = skuList.stream().map(ItemSku::getId).collect(Collectors.toList());
        final List<SkuAttrRefDO> skuAttrRefList = itemGateway.getSkuAttrList(skuIdList);

        List<ItemSkuListDto> list = new LinkedList<>();
        Map<Long, List<SkuAttrRefDO>> attrMap = new HashMap<>(8);
        for (SkuAttrRefDO skuAttrRefDo : skuAttrRefList) {
            final List<SkuAttrRefDO> varList = attrMap.getOrDefault(skuAttrRefDo.getSkuId(), new LinkedList<>());
            varList.add(skuAttrRefDo);
            attrMap.put(skuAttrRefDo.getSkuId(), varList);
        }

        IItemSkuPriceService iItemSkuPriceService = SpringUtil.getBean(IItemSkuPriceService.class);

        final Set<String> skuCodes = skuList.stream().map(ItemSku::getSkuCode)
                .collect(Collectors.toSet());
        final Map<String, BigDecimal> stockSum = warehouseStockGateway.getStockSum(skuCodes);
        for (ItemSku itemSku : skuList) {
            ItemSkuListDto itemSkuListDto = new ItemSkuListDto();
            itemSkuListDto.setId(itemSku.getId());
            itemSkuListDto.setItemId(itemSku.getItemId());
            itemSkuListDto.setSkuCode(itemSku.getSkuCode());
            itemSkuListDto.setSpecifiedSkuCode(itemSku.getProviderSpecifiedCode());
            itemSkuListDto.setTaxRate(itemSku.getTaxRate());
            itemSkuListDto.setPurchaseTaxRate(itemSku.getPurchaseTaxRate());
            itemSkuListDto.setSplitType(itemSku.getSplitType());
            itemSkuListDto.setProps(itemSku.getProps());

            List<ItemSkuPrice> list1 = iItemSkuPriceService.lambdaQuery()
                    .eq(ItemSkuPrice::getSkuCode, itemSku.getSkuCode())
                    .eq(ItemSkuPrice::getType, 0)
                    .orderByDesc(ItemSkuPrice::getId)
                    .select(ItemSkuPrice::getPrice, ItemSkuPrice::getStartTime, ItemSkuPrice::getEndTime)
                    .last("limit 1").list();
            if (CollUtil.isNotEmpty(list1)) {
                itemSkuListDto.setProcurement(list1.get(0).getPrice().toString());
                itemSkuListDto.setProcurementStartDt(list1.get(0).getStartTime());
                itemSkuListDto.setProcurementEndDt(list1.get(0).getEndTime());
            } else {
                itemSkuListDto.setProcurement(itemSku.getCostPrice().toString());
            }

            itemSkuListDto.setSales(itemSku.getSalePrice().toString());
            itemSkuListDto.setBarCode(itemSku.getBarCode());

            final List<SkuAttrRefDO> skuAttrRefDos = attrMap.getOrDefault(itemSku.getId(), new LinkedList<>());
            List<ItemAttrDto> attrList = new LinkedList<>();
            for (SkuAttrRefDO skuAttrRefDo : skuAttrRefDos) {
                ItemAttrDto dd = new ItemAttrDto();
                dd.setId(skuAttrRefDo.getSkuId());
                dd.setValue(skuAttrRefDo.getAttrValue());
                dd.setName(skuAttrRefDo.getAttrName());
                dd.setAttrId(skuAttrRefDo.getAttrId());
                dd.setItemAttrDbId(skuAttrRefDo.getItemAttrDbId());
                attrList.add(dd);
            }
            itemSkuListDto.setAttrList(attrList);
            final long skuStock = Optional.ofNullable(stockSum.get(itemSku.getSkuCode()))
                    .map(BigDecimal::longValue).orElse(0L);
            itemSkuListDto.setStockCount(skuStock);

            BigDecimal contractSalePrice = Objects.nonNull(itemSku.getContractSalePrice()) ? itemSku.getContractSalePrice() : BigDecimal.ZERO;
            final SkuExtraPriceDto contractSalePriceDto = getLatestSkuExtraPrice(itemSku, 2, contractSalePrice, iItemSkuPriceService);

            BigDecimal platformCommission = Objects.nonNull(itemSku.getPlatformCommission()) ? itemSku.getPlatformCommission() : BigDecimal.ZERO;
            final SkuExtraPriceDto platformCommissionDto = getLatestSkuExtraPrice(itemSku, 3, platformCommission, iItemSkuPriceService);

            itemSkuListDto.setContractSalePrice(contractSalePriceDto);
            itemSkuListDto.setPlatformCommission(platformCommissionDto);
            itemSkuListDto.setGoodsType(itemSku.getGoodsType());
            list.add(itemSkuListDto);
        }

        return list;
    }

    private SkuExtraPriceDto getLatestSkuExtraPrice(ItemSku itemSku, Integer type, BigDecimal defaultValue,
                                                    IItemSkuPriceService iItemSkuPriceService) {
        final List<ItemSkuPrice> list = iItemSkuPriceService.lambdaQuery()
                .eq(ItemSkuPrice::getSkuId, itemSku.getId())
                .eq(ItemSkuPrice::getSkuCode, itemSku.getSkuCode())
                .eq(ItemSkuPrice::getType, type)
                .orderByDesc(ItemSkuPrice::getId)
                .list();
        // 直接取最新的一个价格信息，如果没有就直接返回默认值
        if (CollUtil.isNotEmpty(list)) {
            SkuExtraPriceDto dto = new SkuExtraPriceDto();
            dto.setVal(list.get(0).getPrice());
            dto.setStartTime(list.get(0).getStartTime());
            dto.setEndTime(list.get(0).getEndTime());
            dto.setType(dto.getType());
            return dto;
        }

        // 构建默认值
        SkuExtraPriceDto dto = new SkuExtraPriceDto();
        dto.setVal(defaultValue);
        dto.setStartTime(1727712000L);
        dto.setEndTime(0L);
        dto.setType(type);
        return dto;
    }


    public List<ItemDetailImageVo> getImageList(Long itemId, ItemGateway itemGateway) {
        final List<ItemImage> imageList = itemGateway.getImageListByType(itemId, ItemImageType.COMMON);
        return ItemTransMapper.INSTANCE.imageDbToDetailVos(imageList);
    }

    public ItemDetailProcurementVo getProcurement(Long itemId, ItemGateway itemGateway, ProviderGateway providerGateway, BaseInfoService baseInfoService, IWarehouseService warehouseService) {
        ItemDetailProcurementVo vo = new ItemDetailProcurementVo();

        final ItemProcurement procurement = itemGateway.getProcurementByItemId(itemId);
        if (Objects.nonNull(procurement)) {
            vo.setProviderId(procurement.getProviderId());
            vo.setBuyerId(procurement.getBuyerId());
            vo.setDelivery(procurement.getDelivery());
            vo.setTaxRateCode(procurement.getTaxRateCode());
            vo.setPurchaseTaxRate(procurement.getPurchaseRate());
            final Provider provider = providerGateway.getById(procurement.getProviderId());
            vo.setProviderName(Objects.nonNull(provider) ? provider.getName() : "");
            if (Objects.nonNull(procurement.getBaseUnitId())) {
                BaseUnit baseUnit = baseInfoService.ById(procurement.getBaseUnitId());
                if (Objects.nonNull(baseUnit)) {
                    vo.setBaseUnit(baseUnit.getName());
                    vo.setBaseUnitId(baseUnit.getId());
                }
            }
            if (Objects.nonNull(procurement.getRate())) {
                vo.setRate(procurement.getRate());
            }
            if (Objects.nonNull(procurement.getWarehouseNo())) {
                Optional<Warehouse> warehouse = warehouseService.queryWarehouseByNo(procurement.getWarehouseNo());
                if (warehouse.isPresent()) {
                    vo.setWarehouseNo(warehouse.get().getNo());
                    vo.setWarehouseName(warehouse.get().getName());
                }
            }


        }
        final List<ItemExpressTemplate> expressByItemList = itemGateway.getExpressByItemId(itemId);
        final List<ItemExpressVo> collect = expressByItemList.stream().map(ItemTransMapper.INSTANCE::expressDdToDto).collect(Collectors.toList());
        vo.setExpressDtoList(collect);
        return vo;
    }

    public PartnerItemVo getPartner(String partnerProviderItemSn, ItemGateway itemGateway) {
        if (StringUtil.isBlank(partnerProviderItemSn)) {
            return new PartnerItemVo();
        }

        PartnerItemVo vo = new PartnerItemVo();
        PartnerItemCmd cmd = new PartnerItemCmd();
        cmd.setPageIndex(1);
        cmd.setPageSize(5);
        cmd.setSearchType(1);
        cmd.setContext(partnerProviderItemSn);
        final List<PartnerItemResp> partnerItemRespList = itemGateway.partnerQuery(cmd);
        if (CollUtil.isNotEmpty(partnerItemRespList)) {
            final PartnerItemResp partnerItemResp = itemGateway.partnerQuery(cmd).get(0);
            vo = ItemTransMapper.INSTANCE.partnerRespToVo(partnerItemResp);
        }
        return vo;
    }
}

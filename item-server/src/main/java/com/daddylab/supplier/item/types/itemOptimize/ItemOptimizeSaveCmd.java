package com.daddylab.supplier.item.types.itemOptimize;

import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@GroupSequenceProvider(ItemOptimizeSaveCmd.GroupProvider.class)
@Data
public class ItemOptimizeSaveCmd {

    /** ID */
    @ApiModelProperty("ID")
    @NotNull()
    @DistributedLockKey
    private Long id;

    /** 对应销售平台的货品ID */
    @ApiModelProperty(value = "对应销售平台的货品ID")
    @Size(min = 1, max = 100)
    private String outerItemId;

    /**
     * 商品SPU编码
     */
    @ApiModelProperty(value = "商品SPU编码")
    @Size(min = 1, max = 100)
    private String itemCode;

    /** 商品优化模型数据 */
    @ApiModelProperty("商品优化模型数据")
    @NotNull(groups = {ValidateData.class})
    @Valid
    private ItemOptimizeSaveCmdData data;

    /** 版本号（乐观锁） */
    @ApiModelProperty("版本号（乐观锁）")
    @NotNull
    private Integer v;

    /** 是否提交 */
    @ApiModelProperty("是否提交")
    private boolean submit;

    public interface ValidateData {}

    public static class GroupProvider implements DefaultGroupSequenceProvider<ItemOptimizeSaveCmd> {

        @Override
        public List<Class<?>> getValidationGroups(ItemOptimizeSaveCmd object) {
            final ArrayList<Class<?>> groups = new ArrayList<>();
            groups.add(ItemOptimizeSaveCmd.class);
            if (object != null) {
                if (!object.isSubmit()) {
                    groups.add(ValidateData.class);
                }
            }
            return groups;
        }
    }
}

package com.daddylab.supplier.item.domain.item.gateway;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:55 上午
 * @description
 */
public interface ItemExpressGateway {

    void saveOrUpdateBatchExpress(List<ItemExpressTemplate> list);

    List<ItemExpressTemplate> getExpressByItemId(Long itemId);

    void removeExpress(List<Long> idList);

    ItemExpressTemplate getExpressTemplateByItemId(Long itemId);
}

package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.date.StopWatch;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig.ItemTypeWeightConfig;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemSkuStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class InventoryAllocBizServiceImpl implements InventoryAllocBizService {
    
    @Autowired
    IInventoryAllocService inventoryAllocService;
    @Autowired
    IInventoryAllocShopService inventoryAllocShopService;
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    @Autowired
    IPlatformItemService platformItemService;
    @Autowired
    PlatformItemSyncConfig platformItemSyncConfig;
    @Autowired
    IShopService shopService;
    @Autowired
    IWdtStockSpecRtService wdtStockSpecRtService;
    @Autowired
    IInventoryMonitorService inventoryMonitorService;
    @Autowired
    ICombinationItemService combinationItemService;
    @Autowired
    IComposeSkuService composeSkuService;
    
    // =================================================================
    // Public Methods
    // =================================================================
    
    @Override
    public void allocInventory() {
        StopWatch watch = new StopWatch("库存分配");
        
        watch.start("查询待分配店铺");
        List<ShopContext> shopContexts = getShopWeights();
        watch.stop();
        log.info(
                "[库存分配]待分配店铺权重计算完成，共计={} time={}ms",
                shopContexts.size(),
                watch.getLastTaskTimeMillis());
        log.info("[库存分配]店铺权重:{}",
                shopContexts.stream().map(ShopContext::toString).collect(Collectors.joining(",")));
        
        watch.start("查询所有在售链接商品SKU");
        List<String> allOuterSkuCodes = getAllOnSaleOuterSkuCodes();
        watch.stop();
        log.info(
                "[库存分配]查询到所有在售链接商品SKU，去重后共计={}，time={}ms",
                allOuterSkuCodes.size(),
                watch.getLastTaskTimeMillis());
        
        watch.start("按编码分配库存");
        Flux.fromIterable(allOuterSkuCodes)
                .window(10)
                .concatMap(batch -> batch.flatMap(skuCode -> allocInventory(skuCode, shopContexts)))
                .doOnNext(this::saveOrUpdateInventoryAllocs)
                .blockLast();
        watch.stop();
        log.info(
                "[库存分配]库存分配完成，分配耗时={}ms，总耗时={}ms",
                watch.getLastTaskTimeMillis(),
                watch.getTotalTimeMillis());
    }
    
    // =================================================================
    // Core Allocation Logic
    // =================================================================
    
    private Mono<List<InventoryAlloc>> allocInventory(String skuCode, List<ShopContext> shopContexts) {
        log.debug("[库存分配]开始分配商品SKU:{}", skuCode);
        
        CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
        if (combinationItem != null) {
            return allocCombinationItemInventory(
                    new CombinationItemAllocContext().setSkuCode(skuCode).setShopContexts(shopContexts)
                            .setCombinationItem(combinationItem));
        } else {
            return allocRegularItemInventory(skuCode, shopContexts);
        }
    }
    
    /**
     * 分配普通商品库存
     */
    private Mono<List<InventoryAlloc>> allocRegularItemInventory(
            String skuCode, List<ShopContext> shopContexts) {
        RegularItemAllocContext context =
                new RegularItemAllocContext().setSkuCode(skuCode).setShopContexts(shopContexts);
        
        // 查询在售的平台商品SKU
        List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(skuCode);
        if (platformItemSkus.isEmpty()) {
            log.debug("[库存分配][分配普通商品库存]商品SKU={}，未找到关联的在售平台商品SKU，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 2. 查询相关组合装商品
        List<Long> platformItemIds =
                platformItemSkus.stream()
                        .map(PlatformItemSku::getPlatformItemId)
                        .collect(Collectors.toList());
        List<PlatformItem> platformItems = platformItemService.listByIds(platformItemIds);
        
        // 查询套装内包含此单品的组合装
        List<CombinationItem> relatedCombinationItems = combinationItemService.listBySkuCode(skuCode);
        List<PlatformItem> relatedCombinationPiList = Collections.emptyList();
        List<PlatformItemSku> relatedCombinationPisList = Collections.emptyList();
        
        if (!relatedCombinationItems.isEmpty()) {
            List<String> relatedCombinationCodes =
                    relatedCombinationItems.stream()
                            .map(CombinationItem::getCode)
                            .collect(Collectors.toList());
            relatedCombinationPisList =
                    platformItemSkuService
                            .lambdaQuery()
                            .in(PlatformItemSku::getOuterSkuCode, relatedCombinationCodes)
                            .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                            .list();
            if (!relatedCombinationPisList.isEmpty()) {
                List<Long> relatedCombinationPiIds =
                        relatedCombinationPisList.stream()
                                .map(PlatformItemSku::getPlatformItemId)
                                .collect(Collectors.toList());
                relatedCombinationPiList = platformItemService.listByIds(relatedCombinationPiIds);
            }
        }
        
        List<PlatformItem> allPlatformItems = new ArrayList<>();
        allPlatformItems.addAll(platformItems);
        allPlatformItems.addAll(relatedCombinationPiList);
        Map<Long, PlatformItem> platformItemMap =
                allPlatformItems.stream()
                        .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        
        context.setPlatformItemSkus(platformItemSkus).setPlatformItemSkus1(relatedCombinationPisList)
                .setPlatformItemMap(platformItemMap);
        
        // 3. 计算库存信息
        calculateRegularStock(context);
        
        log.debug(
                "[库存分配][分配普通商品库存]商品SKU={}，总库存={}，可用库存={}，警戒库存={}，在售平台商品SKU={}，关联组合装在售的平台商品SKU={}",
                skuCode,
                context.getAllocableStock(),
                context.getAvailableStock(),
                context.getStockAlertThreshold(),
                platformItemIds,
                relatedCombinationPisList.stream().map(PlatformItemSku::getId).collect(Collectors.toList()));
        
        if (context.getAllocableStock().compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("[库存分配][分配普通商品库存]商品SKU={}，总库存为0或负数，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 4. 为每个店铺分配库存
        List<InventoryAlloc> inventoryAllocs = allocRegularStockToShops(context);
        return Mono.just(inventoryAllocs);
    }
    
    /**
     * 分配组合装商品库存
     */
    private Mono<List<InventoryAlloc>> allocCombinationItemInventory(CombinationItemAllocContext context) {
        String skuCode = context.getSkuCode();
        CombinationItem combinationItem = context.getCombinationItem();
        // 查询组合装单品列表
        List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItem.getId());
        context.setComposeSkus(composeSkus);
        context.setComposeSkuCodes(composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList()));
        if (composeSkus.isEmpty()) {
            log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装单品列表为空，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 查询组合装在售的平台商品SKU
        List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(skuCode);
        context.setPlatformItemSkus(platformItemSkus);
        if (platformItemSkus.isEmpty()) {
            log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装在售的平台商品SKU列表为空，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 套内商品SKU
        List<String> composeSkuCodes =
                composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
        
        // 查询组合装套内单品作为独立链接在售的平台商品SKU信息
        List<PlatformItemSku> platformItemSkusForComposeSkus = platformItemSkuService
                .lambdaQuery()
                .in(PlatformItemSku::getOuterSkuCode, composeSkuCodes)
                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                .list();
        context.setPlatformItemSkus1(platformItemSkusForComposeSkus);
        
        // 查询包含当前组合装内单品的其他组合装
        List<CombinationItem> otherRelatedCombinationItems = combinationItemService.listBySkuCode(composeSkuCodes);
        List<String> otherRelatedCombinationItemCodes =
                otherRelatedCombinationItems.stream().map(CombinationItem::getCode).collect(
                        Collectors.toList());
        List<PlatformItemSku> platformItemSkusForOtherRelatedCombination = platformItemSkuService
                .lambdaQuery()
                .in(PlatformItemSku::getOuterSkuCode, otherRelatedCombinationItemCodes)
                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                .list();
        context.setPlatformItemSkus2(platformItemSkusForOtherRelatedCombination);
        
        Set<Long> allRelatedPlatformItemIds =
                Stream.concat(platformItemSkus.stream(), platformItemSkusForComposeSkus.stream())
                        .map(PlatformItemSku::getPlatformItemId)
                        .collect(Collectors.toSet());
        
        List<PlatformItem> allRelatedPlatformItems = platformItemService.listByIds(allRelatedPlatformItemIds);
        Map<Long, PlatformItem> platformItemMap =
                allRelatedPlatformItems.stream()
                        .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        context.setPlatformItemMap(platformItemMap);
        
        // 计算组合装库存信息
        BigDecimal availableCombinationStock = calculateAvailableCombinationStock(context);
        if (availableCombinationStock == null || availableCombinationStock.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("[库存分配][分配组合装商品库存]组合装SKU={}，可用库存为0，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 为每个店铺分配库存
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        BigDecimal remainingCombinationStock = availableCombinationStock;
        ShopContext lastShopContext = context.getShopContexts().get(context.getShopContexts().size() - 1);
        for (ShopContext shopContext : context.getShopContexts()) {
            CombinationItemAllocShopContext allocShopContext =
                    new CombinationItemAllocShopContext(context, shopContext);
            BigDecimal allocableShopCombinationStock =
                    availableCombinationStock.multiply(shopContext.getPercent()).setScale(0, RoundingMode.HALF_UP);
            StringBuilder calculation = new StringBuilder(
                    String.format(
                            "当前店铺可分配库存 = 总可分配库存 * (店铺权重 / 总权重) = %s * (%s / %s) = %s * %s = %s",
                            availableCombinationStock,
                            shopContext.getWeight(), shopContext.getTotalWeight(), availableCombinationStock,
                            shopContext.getPercent(), allocableShopCombinationStock));
            if (lastShopContext == shopContext) {
                allocableShopCombinationStock = remainingCombinationStock;
                calculation.append("，精度修正，覆盖为店铺剩余可分配库存 = ").append(remainingCombinationStock);
            } else {
                remainingCombinationStock = remainingCombinationStock.subtract(allocableShopCombinationStock);
            }
            allocShopContext.setAllocableStock(allocableShopCombinationStock)
                    .setAllocableStockCalculation(calculation.toString());
            List<InventoryAlloc> shopAllocs =
                    allocCombinationStockForShop(allocShopContext);
            inventoryAllocs.addAll(shopAllocs);
        }
        
        return Mono.just(inventoryAllocs);
    }
    
    private List<InventoryAlloc> allocRegularStockToShops(
            RegularItemAllocContext context) {
        
        String skuCode = context.getSkuCode();
        BigDecimal allocableStock = context.getAllocableStock();
        BigDecimal remainStock = allocableStock;
        List<ShopContext> shopContexts = context.getShopContexts();
        
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        for (int i = 0; i < shopContexts.size(); i++) {
            ShopContext shopContext = shopContexts.get(i);
            String shopNo = shopContext.getShopNo();
            
            BigDecimal shopAllocableStock;
            if (i == shopContexts.size() - 1) {
                shopAllocableStock = remainStock;
            } else {
                shopAllocableStock =
                        shopContext.getPercent().multiply(allocableStock).setScale(0, RoundingMode.HALF_UP);
                remainStock = remainStock.subtract(shopAllocableStock);
            }
            
            log.debug("[库存分配][分配普通商品库存]商品SKU={}，店铺={}，分配库存={}", skuCode, shopContext,
                    shopAllocableStock);
            
            if (shopAllocableStock.compareTo(BigDecimal.ZERO) <= 0) {
                log.debug("[库存分配][分配普通商品库存]商品SKU={}，店铺={}，分配库存为0，跳过分配", shopContext,
                        shopNo);
                continue;
            }
            
            RegularItemAllocShopContext regularItemAllocShopContext =
                    new RegularItemAllocShopContext(context, shopContext).setAllocableStock(shopAllocableStock)
                            .setAllocableStockCalculation(
                                    String.format(
                                            "店铺可分配库存 = 总可分配库存 * (店铺权重 / 总权重) = %s * (%s / %s) = %s * %s = %s",
                                            allocableStock,
                                            shopContext.getWeight(), shopContext.getTotalWeight(), allocableStock,
                                            shopContext.getPercent(), shopAllocableStock));
            List<InventoryAlloc> shopAllocs =
                    allocStockForSingleShop(regularItemAllocShopContext);
            inventoryAllocs.addAll(shopAllocs);
        }
        return inventoryAllocs;
    }
    
    private List<InventoryAlloc> allocStockForSingleShop(RegularItemAllocShopContext context) {
        String skuCode = context.getParentContext().getSkuCode();
        ShopContext shopContext = context.getCurrentShopContext();
        String shopNo = shopContext.getShopNo();
        
        List<PlatformItemSku> shopOnSaleSkus =
                context.getPlatformItemSkus().stream()
                        .filter(sku -> sku.getShopNo().equals(shopNo))
                        .collect(Collectors.toList());
        
        if (shopOnSaleSkus.isEmpty()) {
            log.debug("[库存分配]商品SKU={}，店铺{}，未找到关联的平台商品SKU，跳过分配", skuCode, shopNo);
            return Collections.emptyList();
        }
        
        if (shopContext.getPlatform() == Platform.LAOBASHOP) {
            return allocStockByWeight(context);
        } else {
            return allocStockEvenly(context);
        }
    }
    
    private List<InventoryAlloc> allocStockByWeight(
            RegularItemAllocShopContext context) {
        CalculateSkuWeightResult calculateSkuWeightResult = calculateSkuWeight(context);
        Map<Long, CalculateSkuWeightResult.AllocWeight> allocWeights = calculateSkuWeightResult.getAllocWeights();
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        BigDecimal allocableStock = context.getAllocableStock();
        BigDecimal remainingShopStock = allocableStock;
        PlatformItemSku lastSku = context.getPlatformItemSkus().get(context.getPlatformItemSkus().size() - 1);
        for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
            CalculateSkuWeightResult.AllocWeight allocWeight = allocWeights.get(platformItemSku.getId());
            BigDecimal allocStock;
            CalculationLogBuilder calculationProcess = new CalculationLogBuilder()
                    .withShopContext(context.getCurrentShopContext())
                    .withMethod("按照权重分配")
                    .withExpression(context.getParentContext().getAllocableStockCalculation())
                    .withExpression(context.getAllocableStockCalculation())
                    .withExpression(
                            String.format("链接分配库存 = 权重占比 * 可用库存 = (%s) * %s", allocWeight,
                                    allocableStock));
            if (platformItemSku == lastSku) {
                allocStock = remainingShopStock;
                calculationProcess.withRemark("分配剩余库存 = " + allocStock);
            } else {
                allocStock = allocWeight.getPercent().multiply(allocableStock).setScale(0, RoundingMode.HALF_UP);
                remainingShopStock = remainingShopStock.subtract(allocStock);
            }
            calculationProcess.withResult(allocStock.toPlainString());
            
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, allocableStock, allocStock, calculationProcess.build());
            inventoryAllocs.add(inventoryAlloc);
        }
        return inventoryAllocs;
    }
    
    private CalculateSkuWeightResult calculateSkuWeight(RegularItemAllocShopContext context) {
        Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
        int totalWeight = 0;
        LinkedHashMap<Long, CalculateSkuWeightResult.AllocWeight> allocWeights = new LinkedHashMap<>();
        List<PlatformItemSku> skuList =
                Stream.of(context.getPlatformItemSkus(), context.getPlatformItemSkus1())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
        for (PlatformItemSku sku : skuList) {
            PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
            if (platformItem == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                        "平台商品ID为" + sku.getPlatformItemId() + "的记录未查询到");
            }
            ItemTypeWeightConfig itemTypeWeightConfig =
                    platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            totalWeight += itemTypeWeightConfig.getWeight();
        }
        CalculateSkuWeightResult calculateSkuWeightResult = new CalculateSkuWeightResult();
        calculateSkuWeightResult.setTotalWeight(totalWeight);
        for (PlatformItemSku sku : skuList) {
            PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
            ItemTypeWeightConfig itemTypeWeightConfig =
                    platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            BigDecimal weightPercent;
            if (sku == skuList.get(skuList.size() - 1)) {
                weightPercent = BigDecimal.ONE.subtract(
                        allocWeights.values().stream().map(CalculateSkuWeightResult.AllocWeight::getPercent)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                weightPercent = new BigDecimal(itemTypeWeightConfig.getWeight())
                        .divide(new BigDecimal(totalWeight), 6, RoundingMode.HALF_UP);
                
            }
            CalculateSkuWeightResult.AllocWeight allocWeight = calculateSkuWeightResult.createAllocWeight();
            allocWeight.setPlatformItemSkuId(sku.getId());
            allocWeight.setWeight(itemTypeWeightConfig.getWeight());
            allocWeight.setPercent(weightPercent);
            allocWeights.put(sku.getId(), allocWeight);
        }
        calculateSkuWeightResult.setAllocWeights(allocWeights);
        context.setCalculateSkuWeightResult(calculateSkuWeightResult);
        return calculateSkuWeightResult;
    }
    
    private List<InventoryAlloc> allocStockEvenly(
            RegularItemAllocShopContext context) {
        
        List<PlatformItemSku> shopSkus = new ArrayList<>();
        shopSkus.addAll(context.getPlatformItemSkus());
        shopSkus.addAll(context.getPlatformItemSkus1());
        
        BigDecimal allocableStock = context.getAllocableStock();
        BigDecimal avgStock = allocableStock.divide(new BigDecimal(shopSkus.size()), 0, RoundingMode.HALF_UP);
        BigDecimal correction = allocableStock.subtract(avgStock.multiply(new BigDecimal(shopSkus.size())));
        
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        PlatformItemSku lastSku = context.getPlatformItemSkus().get(shopSkus.size() - 1);
        for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
            BigDecimal allocStock;
            CalculationLogBuilder calculationProcess =
                    new CalculationLogBuilder()
                            .withShopContext(context.getCurrentShopContext())
                            .withMethod("平均分配")
                            .withExpression(context.getParentContext().getAllocableStockCalculation())
                            .withExpression(context.getAllocableStockCalculation());
            if (platformItemSku == lastSku) {
                allocStock = avgStock.add(correction);
                calculationProcess.withExpression(
                        String.format("链接分配库存 = 店铺可分配库存 / 在售链接数量 + 精度修正 = %s / %s + %s = %s",
                                allocableStock,
                                shopSkus.size(), correction, allocStock));
            } else {
                allocStock = avgStock;
                calculationProcess.withExpression(
                        String.format("链接分配库存 = 店铺可分配库存 / 在售链接数量 = %s / %s = %s", allocableStock,
                                shopSkus.size(), allocStock));
            }
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, allocableStock, allocStock, calculationProcess.build());
            inventoryAllocs.add(inventoryAlloc);
        }
        return inventoryAllocs;
    }
    
    // =================================================================
    // Combination Item Allocation Helpers
    // =================================================================
    
    private List<InventoryAlloc> allocCombinationStockForShop(CombinationItemAllocShopContext context) {
        ShopContext shopContext = context.getCurrentShopContext();
        if (shopContext.getPlatform() == Platform.LAOBASHOP) {
            return allocCombinationStockByWeight(context);
        } else {
            return allocCombinationStockEvenly(context);
        }
    }
    
    private List<InventoryAlloc> allocCombinationStockByWeight(CombinationItemAllocShopContext context) {
        CalculateSkuWeightResult calculateSkuWeightResult = calculateSkuWeight(context);
        Map<Long, CalculateSkuWeightResult.AllocWeight> allocWeights = calculateSkuWeightResult.getAllocWeights();
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        BigDecimal availableCombinationStock = context.getAllocableStock();
        BigDecimal remainingShopStock = availableCombinationStock;
        PlatformItemSku lastSku = context.getPlatformItemSkus().get(context.getPlatformItemSkus().size() - 1);
        for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
            CalculateSkuWeightResult.AllocWeight allocWeight = allocWeights.get(platformItemSku.getId());
            BigDecimal allocStock;
            CalculationLogBuilder calculationProcess = new CalculationLogBuilder()
                    .withShopContext(context.getCurrentShopContext())
                    .withMethod("按照权重分配")
                    .withExpression(context.getParentContext().getAllocableStockCalculation())
                    .withExpression(context.getAllocableStockCalculation())
                    .withExpression(String.format("链接分配库存 = 权重占比 * 可用库存 = (%s) * %s", allocWeight,
                            availableCombinationStock));
            if (platformItemSku == lastSku) {
                allocStock = remainingShopStock;
                calculationProcess.withExpression("精度修正，分配剩余库存=" + allocStock);
            } else {
                allocStock = allocWeight.getPercent().multiply(availableCombinationStock).setScale(0, RoundingMode.HALF_UP);
                remainingShopStock = remainingShopStock.subtract(allocStock);
            }
            calculationProcess.withResult(allocStock.toPlainString());
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, availableCombinationStock, allocStock,
                            calculationProcess.build());
            inventoryAllocs.add(inventoryAlloc);
        }
        return inventoryAllocs;
    }
    
    private List<InventoryAlloc> allocCombinationStockEvenly(CombinationItemAllocShopContext context) {
        BigDecimal availableCombinationStock = context.getAllocableStock();
        List<PlatformItemSku> shopOnSaleSkus =
                Stream.of(context.getPlatformItemSkus(), context.getPlatformItemSkus1(),
                                context.getPlatformItemSkus2())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        BigDecimal numOfOnSaleSkus = new BigDecimal(shopOnSaleSkus.size());
        BigDecimal avgStock =
                availableCombinationStock.divide(numOfOnSaleSkus, 0, RoundingMode.HALF_UP);
        BigDecimal correctValue = availableCombinationStock.subtract(avgStock.multiply(numOfOnSaleSkus));
        for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
            BigDecimal allocStock = avgStock;
            CalculationLogBuilder calculationProcess =
                    new CalculationLogBuilder()
                            .withShopContext(context.getCurrentShopContext())
                            .withMethod("平均分配");
            if (platformItemSku == context.getPlatformItemSkus().get(context.getPlatformItemSkus().size() - 1)) {
                allocStock = allocStock.add(correctValue);
                calculationProcess.withExpression(
                        String.format("链接分配库存 = 店铺库存 / SKU数量 + 精度修正 = %s / %s + %s = %s",
                                availableCombinationStock,
                                shopOnSaleSkus.size(), correctValue, avgStock));
            } else {
                calculationProcess.withExpression(
                        String.format("链接分配库存 = 店铺库存 / SKU数量 = %s / %s = %s", availableCombinationStock,
                                shopOnSaleSkus.size(), avgStock));
            }
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, availableCombinationStock, allocStock,
                            calculationProcess.build());
            inventoryAllocs.add(inventoryAlloc);
        }
        return inventoryAllocs;
    }
    
    // =================================================================
    // Context Query
    // =================================================================
    
    private List<ShopContext> getShopWeights() {
        List<InventoryAllocShop> allocShopList =
                inventoryAllocShopService
                        .lambdaQuery()
                        .ne(InventoryAllocShop::getStatus, InventoryAllocShopStatus.FORBIDDEN)
                        .list();
        
        if (allocShopList.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Shop> shops =
                shopService.listByIds(
                        allocShopList.stream().map(InventoryAllocShop::getShopId).collect(Collectors.toList()));
        Map<Long, Shop> shopMap =
                shops.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));
        int totalWeight = allocShopList.stream().mapToInt(InventoryAllocShop::getInventoryWeight).sum();
        
        return allocShopList.stream()
                .map(
                        inventoryAllocShop ->
                                new ShopContext(
                                        shopMap.get(inventoryAllocShop.getShopId()),
                                        inventoryAllocShop.getInventoryWeight(),
                                        totalWeight))
                .collect(Collectors.toList());
    }
    
    private List<String> getAllOnSaleOuterSkuCodes() {
        ListOuterSkuCodeQuery listOuterSkuCodeQuery = new ListOuterSkuCodeQuery();
        listOuterSkuCodeQuery.setPlatformItemSkuStatus(
                Lists.newArrayList(PlatformItemSkuStatus.ON_SALE.getValue()));
        return platformItemSkuService.listOuterSkuCode(listOuterSkuCodeQuery);
    }
    
    private List<PlatformItemSku> queryOnSalePlatformItemSkus(String skuCode) {
        return platformItemSkuService
                .lambdaQuery()
                .eq(PlatformItemSku::getOuterSkuCode, skuCode)
                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                .list();
    }
    
    // =================================================================
    // Stock Calculation
    // =================================================================
    
    private void calculateRegularStock(RegularItemAllocContext context) {
        List<WdtStockSpecRt> stockSpecRts =
                wdtStockSpecRtService
                        .lambdaQuery()
                        .eq(WdtStockSpecRt::getSpecNo, context.getSkuCode())
                        .eq(WdtStockSpecRt::getDefect, 0)
                        .list();
        
        BigDecimal availableStockSum =
                stockSpecRts.stream()
                        .map(WdtStockSpecRt::getAvailableStock)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 查询警戒库存
        List<InventoryMonitor> inventoryMonitors =
                inventoryMonitorService
                        .lambdaQuery()
                        .eq(InventoryMonitor::getSkuNo, context.getSkuCode())
                        .eq(InventoryMonitor::getStatus, 1)
                        .eq(InventoryMonitor::getThresholdType, 1)
                        .list();
        
        int stockAlertThresholdSum =
                inventoryMonitors.stream().mapToInt(InventoryMonitor::getAlertThreshold).sum();
        
        BigDecimal stockTotal = availableStockSum.subtract(new BigDecimal(stockAlertThresholdSum));
        
        context.setAvailableStock(availableStockSum);
        context.setStockAlertThreshold(stockAlertThresholdSum);
        context.setAllocableStock(stockTotal);
        context.setAllocableStockCalculation(
                String.format("规格在仓库内总的可链接分配库存 = 可用库存 - 警戒库存 = %s - %s = %s", availableStockSum,
                        stockAlertThresholdSum, stockTotal));
    }
    
    
    private BigDecimal calculateAvailableCombinationStock(CombinationItemAllocContext context) {
        Long combinationId = context.getCombinationItem().getId();
        List<String> composeSkuCodes = context.getComposeSkuCodes();
        log.debug("[库存分配] 计算组合装可用库存，组合装ID: {}，单品列表: {}", combinationId, composeSkuCodes);
        
        // 查询库存信息
        List<WdtStockSpecRt> stockSpecRts =
                wdtStockSpecRtService
                        .lambdaQuery()
                        .in(WdtStockSpecRt::getSpecNo, composeSkuCodes)
                        .eq(WdtStockSpecRt::getDefect, 0)
                        .list();
        
        Map<String, BigDecimal> skuAvailableStockMap =
                stockSpecRts.stream()
                        .collect(
                                Collectors.groupingBy(
                                        WdtStockSpecRt::getSpecNo,
                                        Collectors.reducing(
                                                BigDecimal.ZERO, WdtStockSpecRt::getAvailableStock, BigDecimal::add)));
        context.setSkuAvailableStockMap(skuAvailableStockMap);
        
        // 查询警戒库存
        List<InventoryMonitor> inventoryMonitors =
                inventoryMonitorService
                        .lambdaQuery()
                        .in(InventoryMonitor::getSkuNo, composeSkuCodes)
                        .eq(InventoryMonitor::getStatus, 1)
                        .eq(InventoryMonitor::getThresholdType, 1)
                        .list();
        
        Map<String, Integer> skuAlertThresholdMap =
                inventoryMonitors.stream()
                        .collect(
                                Collectors.groupingBy(
                                        InventoryMonitor::getSkuNo,
                                        Collectors.summingInt(InventoryMonitor::getAlertThreshold)));
        context.setSkuWarnThresholdMap(skuAlertThresholdMap);
        
        BigDecimal combinationStock = null;
        BigDecimal totalNumOfLinks =
                new BigDecimal(context.getPlatformItemSkus().size() + context.getPlatformItemSkus1().size() +
                        context.getPlatformItemSkus2().size());
        ArrayList<String> calculations = new ArrayList<>();
        for (ComposeSku composeSku : context.getComposeSkus()) {
            String composeSkuCode = composeSku.getSkuCode();
            BigDecimal skuAvailableStock =
                    skuAvailableStockMap.getOrDefault(composeSkuCode, BigDecimal.ZERO);
            Integer alertThreshold = skuAlertThresholdMap.getOrDefault(composeSkuCode, 0);
            log.debug("[库存分配] 计算组合装可用库存，单品: {}, 可用库存: {}, 警戒库存: {}", composeSkuCode,
                    skuAvailableStock, alertThreshold);
            
            // 实际可用库存 = 可用库存 - 警戒库存
            BigDecimal actualAvailableStock = skuAvailableStock.subtract(new BigDecimal(alertThreshold));
            if (actualAvailableStock.compareTo(BigDecimal.ZERO) <= 0) {
                log.debug("[库存分配] 计算组合装可用库存，单品: {}, 实际可用库存为0，组合装库存为0", composeSkuCode);
                combinationStock = BigDecimal.ZERO;
                break;
            }
            
            // 计算单品可组成的最大套装数量
            BigDecimal maxCombinations =
                    actualAvailableStock.divide(totalNumOfLinks, 0, RoundingMode.DOWN);
            calculations.add(
                    String.format(
                            "单品 %s 可组成最大套装数量 = 实际可用库存 / 总链接数 = (可用库存 - 警戒库存) / 总链接数 = (%s - %s) / %s = %s",
                            composeSkuCode, skuAvailableStock, alertThreshold, totalNumOfLinks, maxCombinations));
            log.debug("[库存分配] 计算组合装可用库存，单品: {}, 可组成最大套装数量: {}", composeSkuCode,
                    maxCombinations);
            
            // 取最小值作为实际可组装数量
            if (combinationStock == null || maxCombinations.compareTo(combinationStock) < 0) {
                combinationStock = maxCombinations;
                log.debug("[库存分配] 计算组合装可用库存， 更新组合装库存为: {}", combinationStock);
            }
        }
        
        log.debug("[库存分配] 计算组合装可用库存，计算结果: {}", combinationStock);
        context.setAllocableStock(combinationStock)
                .setAllocableStockCalculation("MIN(\n" + String.join(",\n", calculations) + "\n)");
        return combinationStock;
    }
    
    // =================================================================
    // Utility Methods
    // =================================================================
    
    @Data
    private static class CalculateSkuWeightResult {
        private Integer totalWeight;
        private Map<Long, AllocWeight> allocWeights;
        
        public AllocWeight createAllocWeight() {
            return new AllocWeight();
        }
        
        @Data
        private class AllocWeight {
            private Long platformItemSkuId;
            private Integer weight;
            private BigDecimal percent;
            
            @Override
            public String toString() {
                return String.format("%s / %s = %s", weight, totalWeight, percent.toPlainString());
            }
        }
    }
    
    private CalculateSkuWeightResult calculateSkuWeight(CombinationItemAllocShopContext context) {
        Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
        int totalWeight = 0;
        LinkedHashMap<Long, CalculateSkuWeightResult.AllocWeight> allocWeights = new LinkedHashMap<>();
        List<PlatformItemSku> skuList =
                Stream.of(context.getPlatformItemSkus(), context.getPlatformItemSkus1(),
                                context.getPlatformItemSkus2())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
        for (PlatformItemSku sku : skuList) {
            PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
            if (platformItem == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                        "平台商品ID为" + sku.getPlatformItemId() + "的记录未查询到");
            }
            ItemTypeWeightConfig itemTypeWeightConfig =
                    platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            totalWeight += itemTypeWeightConfig.getWeight();
        }
        CalculateSkuWeightResult calculateSkuWeightResult = new CalculateSkuWeightResult();
        calculateSkuWeightResult.setTotalWeight(totalWeight);
        for (PlatformItemSku sku : skuList) {
            PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
            ItemTypeWeightConfig itemTypeWeightConfig =
                    platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            BigDecimal weightPercent;
            if (sku == skuList.get(skuList.size() - 1)) {
                weightPercent = BigDecimal.ONE.subtract(
                        allocWeights.values().stream().map(CalculateSkuWeightResult.AllocWeight::getPercent)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                weightPercent = new BigDecimal(itemTypeWeightConfig.getWeight())
                        .divide(new BigDecimal(totalWeight), 6, RoundingMode.HALF_UP);
                
            }
            CalculateSkuWeightResult.AllocWeight allocWeight = calculateSkuWeightResult.createAllocWeight();
            allocWeight.setPlatformItemSkuId(sku.getId());
            allocWeight.setWeight(itemTypeWeightConfig.getWeight());
            allocWeight.setPercent(weightPercent);
            allocWeights.put(sku.getId(), allocWeight);
        }
        calculateSkuWeightResult.setAllocWeights(allocWeights);
        context.setCalculateSkuWeightResult(calculateSkuWeightResult);
        return calculateSkuWeightResult;
    }
    
    
    private InventoryAlloc createInventoryAlloc(
            PlatformItemSku platformItemSku,
            BigDecimal shopStock,
            BigDecimal allocStock,
            String calculationProcess) {
        InventoryAlloc inventoryAlloc = new InventoryAlloc();
        inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
        inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
        inventoryAlloc.setSkuCode(platformItemSku.getSkuCode());
        inventoryAlloc.setTotalInventoryNum(shopStock.intValue());
        inventoryAlloc.setCalcNum(allocStock.intValue());
        inventoryAlloc.setEffectiveNum(allocStock.intValue());
        inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
        inventoryAlloc.setCalculation(calculationProcess);
        log.debug("[库存分配]创建库存分配记录: {}", inventoryAlloc);
        return inventoryAlloc;
    }
    
    private void saveOrUpdateInventoryAllocs(List<InventoryAlloc> inventoryAllocs) {
        if (inventoryAllocs.isEmpty()) {
            return;
        }
        
        Set<Long> skuIds =
                inventoryAllocs.stream()
                        .map(InventoryAlloc::getPlatformItemSkuId)
                        .collect(Collectors.toSet());
        
        // Query existing records by platformItemSkuId
        List<InventoryAlloc> existingAllocs =
                inventoryAllocService.lambdaQuery().in(InventoryAlloc::getPlatformItemSkuId, skuIds).list();
        
        Map<Long, InventoryAlloc> existingAllocMap =
                existingAllocs.stream()
                        .collect(Collectors.toMap(InventoryAlloc::getPlatformItemSkuId, Function.identity()));
        
        List<InventoryAlloc> toInsert = new ArrayList<>();
        List<InventoryAlloc> toUpdate = new ArrayList<>();
        
        for (InventoryAlloc alloc : inventoryAllocs) {
            InventoryAlloc existing = existingAllocMap.get(alloc.getPlatformItemSkuId());
            if (existing != null) {
                // Update existing record
                existing.setTotalInventoryNum(alloc.getTotalInventoryNum());
                existing.setCalcNum(alloc.getCalcNum());
                existing.setEffectiveNum(alloc.getEffectiveNum());
                existing.setLastAllocTime(alloc.getLastAllocTime());
                existing.setCalculation(alloc.getCalculation());
                toUpdate.add(existing);
            } else {
                // Insert new record
                toInsert.add(alloc);
            }
        }
        
        if (!toInsert.isEmpty()) {
            inventoryAllocService.saveBatch(toInsert);
            log.debug("[库存分配][保存结果]批量插入分配结果，数量: {}", toInsert.size());
        }
        
        if (!toUpdate.isEmpty()) {
            inventoryAllocService.updateBatchById(toUpdate);
            log.debug("[库存分配][保存结果]批量更新分配结果，数量: {}", toUpdate.size());
        }
    }
    
    // =================================================================
    // Inner Data Classes
    // =================================================================
    
    /**
     * 计算过程日志构建器
     */
    private static class CalculationLogBuilder {
        private final StringBuilder log = new StringBuilder();
        
        public CalculationLogBuilder withShopContext(ShopContext context) {
            log.append("店铺: ").append(context).append("\n");
            return this;
        }
        
        public CalculationLogBuilder withMethod(String method) {
            log.append("计算方式: ").append(method).append("\n");
            return this;
        }
        
        public CalculationLogBuilder withExpression(String expression) {
            log.append("计算过程: ").append(expression).append("\n");
            return this;
        }
        
        public CalculationLogBuilder withResult(String result) {
            log.append("计算结果: ").append(result).append("\n");
            return this;
        }
        
        public CalculationLogBuilder withRemark(String remark) {
            log.append("备注: ").append(remark).append("\n");
            return this;
        }
        
        public String build() {
            return log.toString();
        }
    }
    
    /**
     * 组合装库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class CombinationItemAllocContext {
        /**
         * 组合装编码
         */
        private String skuCode;
        /**
         * 店铺权重信息
         */
        private List<ShopContext> shopContexts;
        /**
         * 组合装
         */
        private CombinationItem combinationItem;
        /**
         * 当前组合装内包含的单品
         */
        private List<ComposeSku> composeSkus;
        /**
         * 当前组合装内包含的单品编码
         */
        private List<String> composeSkuCodes;
        /**
         * 当前组合装作为独立链接在售的SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 当前组合装内单品作为独立链接在售的SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 当前组合装内单品被包含在其他组合装内进行销售的SKU
         */
        private List<PlatformItemSku> platformItemSkus2;
        /**
         * 上述所有平台商品的映射
         */
        private Map<Long, PlatformItem> platformItemMap;
        /**
         * 当前组合装内单品的可用库存映射
         */
        private Map<String, BigDecimal> skuAvailableStockMap;
        /**
         * 当前组合装内单品的警戒库存映射
         */
        private Map<String, Integer> skuWarnThresholdMap;
        /**
         * 当前组合装的可用库存
         */
        private BigDecimal allocableStock;
        /**
         * 可分配库存计算过程
         */
        private String allocableStockCalculation;
    }
    
    /**
     * 组合装库存分配上下文
     */
    @Getter
    @Accessors(chain = true)
    private static class CombinationItemAllocShopContext {
        /**
         * 父级上下文
         */
        private final CombinationItemAllocContext parentContext;
        /**
         * 当前店铺权重信息
         */
        private final ShopContext currentShopContext;
        /**
         * 当前组合装作为独立链接在售的SKU
         */
        private final List<PlatformItemSku> platformItemSkus;
        /**
         * 当前组合装内单品作为独立链接在售的SKU
         */
        private final List<PlatformItemSku> platformItemSkus1;
        /**
         * 当前组合装内单品被包含在其他组合装内进行销售的SKU
         */
        private final List<PlatformItemSku> platformItemSkus2;
        /**
         * 当前组合装在当前店铺的可用库存
         */
        private @Setter BigDecimal allocableStock;
        /**
         * 可分配库存计算过程
         */
        private @Setter String allocableStockCalculation;
        /**
         * 当前组合装内单品的分配权重信息
         */
        private @Setter CalculateSkuWeightResult calculateSkuWeightResult;
        
        public CombinationItemAllocShopContext(CombinationItemAllocContext parentContext,
                                               ShopContext currentShopContext) {
            this.parentContext = parentContext;
            this.currentShopContext = currentShopContext;
            this.platformItemSkus = parentContext.platformItemSkus.stream()
                    .filter(sku -> sku.getShopNo().equals(currentShopContext.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                    .filter(sku -> sku.getShopNo().equals(currentShopContext.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus2 = parentContext.platformItemSkus2.stream()
                    .filter(sku -> sku.getShopNo().equals(currentShopContext.getShopNo()))
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 普通商品库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class RegularItemAllocContext {
        private String skuCode;
        private List<ShopContext> shopContexts;
        /**
         * 普通商品在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 包含此商品SKU的组合装在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 平台商品映射
         */
        private Map<Long, PlatformItem> platformItemMap;
        /**
         * 可用库存
         */
        private BigDecimal availableStock;
        /**
         * 警戒库存
         */
        private Integer stockAlertThreshold;
        /**
         * 可链接分配库存 = 可用库存 - 警戒库存
         */
        private BigDecimal allocableStock;
        /**
         * 可分配库存计算过程
         */
        private String allocableStockCalculation;
    }
    
    /**
     * 普通商品库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class RegularItemAllocShopContext {
        private RegularItemAllocContext parentContext;
        private ShopContext currentShopContext;
        /**
         * 普通商品在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 包含此商品SKU的组合装在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 店铺可分配库存 = 总可分配库存 * (店铺权重 / 总权重)
         */
        private BigDecimal allocableStock;
        /**
         * 店铺可分配库存计算过程
         */
        private String allocableStockCalculation;
        private CalculateSkuWeightResult calculateSkuWeightResult;
        
        public RegularItemAllocShopContext(RegularItemAllocContext parentContext, ShopContext currentShopContext) {
            this.parentContext = parentContext;
            this.currentShopContext = currentShopContext;
            this.platformItemSkus = parentContext.platformItemSkus.stream()
                    .filter(sku -> sku.getShopNo().equals(currentShopContext.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                    .filter(sku -> sku.getShopNo().equals(currentShopContext.getShopNo()))
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 店铺权重信息
     */
    private static class ShopContext {
        private final @Getter String shopNo;
        private final @Getter Platform platform;
        private final @Getter Integer weight;
        private final @Getter Integer totalWeight;
        private final @Getter BigDecimal percent;
        
        public ShopContext(Shop shop, Integer weight, Integer totalWeight) {
            this.shopNo = shop.getSn();
            this.platform = shop.getPlatform() != null ? shop.getPlatform() : Platform.OTHER;
            this.weight = weight;
            this.totalWeight = totalWeight;
            if (totalWeight == 0) {
                this.percent = BigDecimal.ZERO;
            } else {
                this.percent =
                        new BigDecimal(weight)
                                .divide(new BigDecimal(totalWeight), 6, RoundingMode.HALF_UP);
            }
        }
        
        @Override
        public String toString() {
            return String.format(
                    "平台=%s 编号=%s 权重=%s/%s=%s", platform.getDesc(), shopNo, weight, totalWeight,
                    percent.toPlainString());
        }
    }
}

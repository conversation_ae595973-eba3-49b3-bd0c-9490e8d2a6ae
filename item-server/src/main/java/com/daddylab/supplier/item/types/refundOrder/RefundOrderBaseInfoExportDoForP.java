package com.daddylab.supplier.item.types.refundOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
@Data
public class RefundOrderBaseInfoExportDoForP {

    /**
     * 退款单号
     **/
    @ExcelProperty("退款单号")
    String refundOrderNo;

    /**
     * 原始订单号
     **/
    @ExcelProperty("原始单号")
    String srcOrderNos;

    @ExcelProperty("仓库名称")
    String returnWarehouseName;

    /**
     * 售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款
     **/
    @ExcelProperty("售后类型")
    String type;

    @ExcelProperty("收货状态")
    String receiveState;

    @ExcelProperty("退回物流")
    String returnLogisticsName;

    @ExcelProperty("退回物流单号")
    String returnLogisticsNo;

    /**
     * 商品名称
     **/
    @ExcelProperty("商品名称")
    String itemName;

    /**
     * 规格名称
     **/
    @ExcelProperty("规格")
    String specName;

    /**
     * 退货/退货数量
     **/
    @ExcelProperty("数量")
    Integer refundQuantity;

    /**
     * 创建时间
     **/
    @ExcelProperty("创建时间")
    String createTime;

    /**
     * 承担类型 PERCENTAGE 按百分比 AMOUNT 按金额
     */
    @ExcelProperty("承担类型")
    private String undertakeType;

    /**
     * 承担金额/占比（按金额时单位为元）
     */
    @ExcelProperty("承担金额/占比")
    private String undertakeAmount;

}

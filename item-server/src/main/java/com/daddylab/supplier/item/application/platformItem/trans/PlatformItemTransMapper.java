package com.daddylab.supplier.item.application.platformItem.trans;

import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetail;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetailSku;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnListItem;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnWithItemData;
import com.daddylab.supplier.item.common.trans.TimeTransMapper;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/29
 */
@Mapper(uses = {
        TimeTransMapper.class
}, imports = {
        Platform.class,
        ObjectUtil.class
})
public interface PlatformItemTransMapper {
    PlatformItemTransMapper INSTANCE = Mappers.getMapper(PlatformItemTransMapper.class);

    /**
     * 组装平台商品详情
     *
     * @param platformItem  平台商品PO
     * @param itemBaseData  商品基础数据
     * @param itemImageUrls 商品图片URLs
     * @return 平台商品详情
     */
    @Mapping(target = "itemType", source = "platformItem.type")
    @Mapping(target = "platformIcon", expression = "java(platformItem.getPlatform().getIcon())")
    @Mapping(target = "updatedAt", source = "platformItem.modified")
    @Mapping(target = "itemImages", source = "itemImageUrls")
    @Mapping(target = "id", source = "platformItem.id")
    @Mapping(target = "categoryId", source = "platformItem.categoryId")
    @Mapping(target = "categoryPath", source = "itemBaseData.categoryPath")
    @Mapping(target = "skus", ignore = true)
    @Mapping(target = "firstSalesTime", ignore = true)
    @Mapping(target = "firstListingTime", ignore = true)
    @Mapping(target = "availableStock", ignore = true)
    @Mapping(target = "syncEnabled", source = "platformItem.syncEnabled")
    @Mapping(target = "lockEnabled", source = "platformItem.lockEnabled")
    PlatformItemDetail toPlatformItemDetail(PlatformItem platformItem, ItemBaseDO itemBaseData , List<String> itemImageUrls);

    List<PlatformItemDetailSku> platformItemSkuListToPlatformItemDetailSkuList(List<PlatformItemSku> skuList);

    /**
     * 分割类目路径
     *
     * @param categoryPath 字符串路径
     * @return 路径数组
     */
    default String[] splitCategoryPath(String categoryPath) {
        if (StringUtil.isBlank(categoryPath)) {
            return new String[0];
        }
        return Arrays.stream(categoryPath.split("/")).filter(StringUtil::isNotBlank).toArray(String[]::new);
    }

    /**
     * 组装平台商品警告列表对象
     *
     * @param platformItemWarn 平台商品警告PO
     * @param itemBaseData     商品基本数据
     * @param operator         操作人
     * @param shopPrincipal    店铺负责人
     * @return 平台商品警告列表对象
     */
    @Mapping(target = "platformIcon", expression = "java(ObjectUtil.safeGet(platformItemWarn.getPlatform(), Platform::getIcon, \"\"))")
    @Mapping(target = "id", source = "platformItemWarn.id")
    @Mapping(target = "platformItemId", source = "platformItemWarn.platformItemId")
    @Mapping(target = "status", source = "platformItemWarn.platformItemStatus")
    @Mapping(target = "warnStatus", source = "platformItemWarn.warnStatus")
    @Mapping(target = "warnType", source = "platformItemWarn.warnType")
    @Mapping(target = "operatorName", source = "operator.nickname")
    @Mapping(target = "operatorId", source = "operator.userId")
    @Mapping(target = "operateTime", source = "platformItemWarn.updatedAt")
    @Mapping(target = "itemName", source = "platformItemWarn.goodsName")
    @Mapping(target = "itemImage", source = "itemBaseData.mainImgUrl")
    @Mapping(target = "warnDesc", expression = "java(platformItemWarn.getWarnType().getDesc())")
    @Mapping(target = "skuId", source = "platformItemWarn.skuId")
    @Mapping(target = "skuCode", source = "platformItemWarn.skuCode")
    @Mapping(target = "shopPrincipalName", source = "shopPrincipal.nickname")
    @Mapping(target = "shopPrincipalId", source = "shopPrincipal.userId")
    @Mapping(target = "modified", source = "platformItemWarn.modified")
    PlatformItemWarnListItem toPlatformItemWarnListItem(PlatformItemWarnWithItemData platformItemWarn
            , ItemBaseDO itemBaseData, StaffInfo operator, StaffInfo shopPrincipal);
}

package com.daddylab.supplier.item.types.oa;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class OaCgfkCallback {
  /** 实际上回传的是【采购付款申请单编号】 */
  @ApiModelProperty("流程编号")
  private String processInstId;

  @ApiModelProperty("节点名")
  private String nodeName;

  @ApiModelProperty("用户登录名")
  private String userLoginName;

  @ApiModelProperty("流程提交1，流程回退2，流程结束3，流程撤销4，流程终止5")
  private Integer status;

  @ApiModelProperty(
      "银联付款返回状态 枚举，付款成功-7787722448457350501（常见），付款待提交3774171079321600355，付款流程中2761429546314518108，\n"
          + "等待银行结果-3390025052419045470（常见），付款已删除-6961217234282395739，付款不存在1224098917296457449，\n"
          + "付款已打回3023509932396856099（银企直连系统审核状态，和OA审批无关），付款失败8881915083509351400（常见），其他状态9083445423497300536t ")
  private Long paymentStatus;

  /** 7 流程直接重走。 15，16，17，18 直接提交给我。 */
  @ApiModelProperty("流程子状态")
  private Integer subState;

  @ApiModelProperty("我方银行账号")
  private String ourAccountNumber;

  @ApiModelProperty("我方账户名称")
  private String ourAccountName;

  @ApiModelProperty("我方开户行")
  private String ourBankName;
}

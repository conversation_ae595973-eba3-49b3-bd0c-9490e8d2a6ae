package com.daddylab.supplier.item.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/04/24
 */
@EnableConfigurationProperties(OtherStockInOutConfig.class)
@ConfigurationProperties(prefix = "other")
@Component
@RefreshScope
@Data
public class OtherStockInOutConfig {

    /**
     * 其他出入库需要对仓库进行筛选
     */
    private List<String> warehouseNos;

}
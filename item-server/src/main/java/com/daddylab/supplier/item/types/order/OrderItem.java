package com.daddylab.supplier.item.types.order;


import com.daddylab.supplier.item.infrastructure.jackson.BigDecimalScale4Serializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2022/4/16
 */
@Data
@ApiModel("订单商品")
public class OrderItem {

    /**
     * 订单商品明细ID
     **/
    @ApiModelProperty("订单商品明细ID")
    Long id;

    /**
     * 规格名称
     **/
    @ApiModelProperty("规格名称")
    String specName;

    /**
     * 商品SKU
     **/
    @ApiModelProperty("商品SKU")
    String specNo;

    /**
     * 单价
     **/
    @ApiModelProperty("单价")
    @JsonSerialize(using = BigDecimalScale4Serializer.class)
    BigDecimal price;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    Integer quantity;


    /**
     * 平台状态: 10、未确认 20、待尾款 30、待发货 40、部分发货 50、已发货 60、已签收 70、已完成 80、已退款 90、已关闭
     **/
    @ApiModelProperty("平台状态: 10、未确认 20、待尾款 30、待发货 40、部分发货 50、已发货 60、已签收 70、已完成 80、已退款 90、已关闭")
    Integer platformStatus;

    /**
     * 商品ID
     **/
    @ApiModelProperty("商品ID")
    Long itemId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String itemNo;

    /**
     * 商品名称
     **/
    @ApiModelProperty("商品名称")
    String itemName;

    /**
     * 商品主图
     */
    @ApiModelProperty("商品主图")
    String itemMainImg;

    /**
     * 平台商品ID
     **/
    @ApiModelProperty("平台商品ID")
    Long platformItemId;

    /**
     * 平台商品名称
     **/
    @ApiModelProperty("平台商品名称")
    String platformItemName;

    /**
     * 平台规格名称
     **/
    @ApiModelProperty("平台规格名称")
    String platformSkuName;

    /**
     * 外部平台商品ID
     **/
    @ApiModelProperty("外部平台商品ID")
    String outerPlatformItemId;

    /**
     * 外部平台商品编号
     **/
    @ApiModelProperty("外部平台商品编号")
    String outerPlatformItemNo;

    /**
     * 外部平台商品ID
     **/
    @ApiModelProperty("外部平台规格ID")
    String outerPlatformSkuId;

    /**
     * 外部平台商品编号
     **/
    @ApiModelProperty("外部平台规格编号")
    String outerPlatformSkuNo;







}

package com.daddylab.supplier.item.common.enums;

import com.alibaba.cola.exception.BizException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/22 3:57 下午
 * @description
 */
@Getter
public enum ErrorCode {
    /**
     * 系统异常。
     */
    SYS_ERROR("0001", "系统异常"),
    /**
     * 参数校验异常
     */
    VERIFY_PARAM("0002", "参数非法"),
    HTTP_METHOD_NOT_SUPPORT("0003", "HTTP请求方式错误"),
    HTTP_API_RESOURCE_NOT_VALID("0004", "API资源未定义"),
    UN_KNOW_ERROR("0005", "未知异常"),
    SYSTEM_BUSY("0006", "系统繁忙，请稍后再试"),

    /**
     * 登陆失败
     */
    NO_LOGIN("1001", "未登录"),
    /**
     * OAuth认证失败
     */
    CAS_AUTH_FAIL("1004", "CAS服务认证失败"),
    /**
     * token非法，无法从token中解析出userId
     */
    ILLEGAL_TOKEN("1002", "token非法"),
    /**
     * 无权限
     */
    NO_ACCESS("1003", "无权限"),
    /**
     * 数据未查询到
     */
    DATA_NOT_FOUND("1004", "数据不存在"),
    DUPLICATE_DATA("1005", "数据重复冲突"),
    /**
     * 操作拒绝
     */
    OPERATION_REJECT("1005", "拒绝操作"),
    GATEWAY_ERROR("1006", "网络波动，请稍后再试"),
    LOCK_FAIL("1007", "锁定当前数据失败"),
    URGE_TOO_FREQUENT("1008", "已催促相关负责人，请30min之后再试"),
    NEED_CONFIRM("1009", "请确认操作"),
    STATE_ERR("1010", "数据已变更，请刷新页面后重试"),
    GATEWAY_BUSY("1011", "下游服务繁忙，请稍后再试"),
    UNSUPPORTED_OPERATION("1012", "暂不支持此操作"),

    API_RESP_ERROR("2001", "api响应异常"),
    API_REQ_ERROR("2002", "api请求异常"),
    /**
     * 导入异常
     */
    IMPORT_DATA_ERROR("2020", "导入数据异常"),

    ITEM_BIZ_ERROR("3001", "商品业务异常"),

    REGION_CODE_ERROR("4001", "地区编码未找到"),

    FILE_UPLOAD_ERROR("5001", "文件上传异常"),

    SMS_AUTH_ERROR("6001", "短信验证异常"),

    FILE_ERROR("7001", "文件处理异常"),

    MESSAGE_ERROR("8001", "消息中心业务异常"),

    PLATFORM_ITEM_ERROR("9001", "平台商品业务异常"),
    PLATFORM_ITEM_STOCK_SYNC_ERROR("9009", "平台商品库存同步失败"),

    EMAIL_ERROR("10001", "邮件服务异常"),

    TEXT_PROMPT("0001", "文本提示"),

    THIRD_PARAM_ERROR("11001", "第三方对接处理异常"),
    SHOP_AUTHORIZATION_EXPIRED("11002", "店铺授权过期"),


    BUSINESS_OPERATE_ERROR("12001", "操作异常"),

    KING_DEE_TIME_OUT("13001", "金蝶接口响应超时"),

    RED_BOOK_ERROR("14001", "小红书接口异常"),

    KUAI_SHOU_ERROR("15001", "快手接口异常"),

    PDD_ERROR("16001", "拼多多接口异常"),

    MALL_API_ERROR("17001", "小程序商城接口异常"),

    KDY_ERROR("20001", "快刀云异常"),
    KDY_SUBSCRIBE_NO_MAPPING("20002", "快刀云订阅异常，物流公司名称未映射"),
    KDY_SUBSCRIBE_NO_MOBILE("20003", "快刀云订阅异常，无法找到收件人手机号"),
    KDY_SUBSCRIBE_MOBILE_NOT_DECRYPT("20003", "快刀云订阅异常，订单收件人信息暂未解密"),
    KDY_SUBSCRIBE_MOBILE_CANT_DECRYPT("2004", "快刀云订阅异常，订单收件人信息无法解密"),

    WIKI_ERROR("21001", "WIKI接口异常"),

    PROCESS_ERROR("66001", "流程异常"),
    ;


    /**
     * 错误码
     */
    private final String code;

    /**
     * 响应信息
     */
    private final String msg;

    ErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public boolean is(String code) {
        return this.code.equals(code);
    }

    public boolean is(BizException bizException) {
        return this.code.equals(bizException.getErrCode());
    }


}


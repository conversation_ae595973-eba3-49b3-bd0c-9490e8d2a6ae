package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.item.itemRunning.cmd.ItemRunningCmd;
import com.daddylab.supplier.item.application.item.itemRunning.vo.ItemRunningVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemRunning;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ItemRunningTransMapper.java
 * @description
 * @createTime 2022年04月29日 18:15:00
 */
@Mapper
public interface ItemRunningTransMapper {

    ItemRunningTransMapper INSTANCE = Mappers.getMapper(ItemRunningTransMapper.class);

    List<ItemRunningVo> doToVos(List<ItemRunning> itemRunnings);

    ItemRunningVo doToVo(ItemRunning itemRunning);

    List<ItemRunning> cmdToDos(List<ItemRunningCmd> cmds);

    ItemRunning cmdToDo(ItemRunningCmd cmd);
}

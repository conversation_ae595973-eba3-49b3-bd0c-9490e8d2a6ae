package com.daddylab.supplier.item.types.goodsSn;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnQuery.GroupProvider;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Data
@GroupSequenceProvider(GroupProvider.class)
public class OpenGoodsSnQuery {

    @Size(min = 1)
    @ApiModelProperty(name = "序列号集合", value = "查询序列号相关的数据")
    private Collection<String> snList;

    @Length(min = 1)
    @ApiModelProperty(name = "店铺编号")
    private String shopNo;

    @NotNull(groups = Time.class)
    @ApiModelProperty(name = "查询开始时间")
    private LocalDateTime timeStart;

    @NotNull(groups = Time.class)
    @ApiModelProperty(name = "查询结束时间")
    private LocalDateTime timeEnd;

    @ApiModelProperty(name = "查询时间跨度")
    @Max(value = 30, message = "时间跨度不能超过{value}天", groups = Time.class)
    private Long getTimeSpan() {
        if (timeEnd != null && timeStart != null) {
            return LocalDateTimeUtil.between(timeStart, timeEnd, ChronoUnit.DAYS);
        }
        return null;
    }

    public boolean isEmpty() {
        return BeanUtil.isEmpty(this);
    }

    public interface Time {

    }

    public static class GroupProvider implements DefaultGroupSequenceProvider<OpenGoodsSnQuery> {

        @Override
        public List<Class<?>> getValidationGroups(OpenGoodsSnQuery object) {
            final ArrayList<Class<?>> groups = new ArrayList<>();
            groups.add(OpenGoodsSnQuery.class);
            if (object == null) {
                return groups;
            }
            if (Objects.nonNull(object.getTimeStart()) || Objects.nonNull(object.getTimeEnd())) {
                groups.add(Time.class);
            }
            return groups;
        }
    }

}

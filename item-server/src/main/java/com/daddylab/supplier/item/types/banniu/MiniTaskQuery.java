package com.daddylab.supplier.item.types.banniu;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Data
public class MiniTaskQuery {
    @NotNull
    private Integer projectId;
    private Integer pageNum;
    private Integer pageSize;
    private TaskStatus taskStatus;
    private LocalDateTime modifiedStart;
    private LocalDateTime modifiedEnd;
    private LocalDateTime createdStart;
    private LocalDateTime createdEnd;
    private List<Condition> conditionColumn;
    private FilterColumnIds filterColumnIds;
}

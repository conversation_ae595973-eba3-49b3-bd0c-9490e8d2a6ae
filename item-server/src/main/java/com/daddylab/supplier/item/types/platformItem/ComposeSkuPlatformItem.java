package com.daddylab.supplier.item.types.platformItem;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComposeSkuPlatformItem {
    private String shopNo;
    private Platform platform;
    private Long combinationId;
    private String combinationCode;
    private Long composeSkuId;
    private String skuCode;
    private String itemCode;
    private Long skuId;
    private Long itemId;
    private Integer num;
    private Long platformItemSkuId;
    private String outerSkuId;
    private String outerItemId;
    private String outerSkuCode;
    private String outerItemCode;
}

package com.daddylab.supplier.item.common.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel
@Data
public class IdCmd {
    @NotNull(message = "ID不能为空")
    @ApiModelProperty(name = "id", required = true)
    Long id;

    @ApiModelProperty("直播话术ID")
    Long liveVerbalTrickId;
}

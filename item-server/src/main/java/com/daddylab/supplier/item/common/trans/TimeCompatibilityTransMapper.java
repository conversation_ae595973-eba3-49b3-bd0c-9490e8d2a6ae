package com.daddylab.supplier.item.common.trans;

import static com.daddylab.supplier.item.infrastructure.utils.DateUtil.parseCompatibility;

import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TimeCompatibilityTransMapper {

    TimeCompatibilityTransMapper INSTANCE = Mappers.getMapper(TimeCompatibilityTransMapper.class);

    /**
     * 将各种形式的时间格式化为时间对象
     *
     * @param time 时间戳
     * @return 格式化时间
     */
    default LocalDateTime parseLocalDateTime(Long time) {
        return parseCompatibility(time.toString());
    }

    /**
     * 将各种形式的时间格式化为时间对象
     *
     * @param time 时间戳
     * @return 格式化时间
     */
    default LocalDateTime parseLocalDateTime(String time) {
        return parseCompatibility(time);
    }
}

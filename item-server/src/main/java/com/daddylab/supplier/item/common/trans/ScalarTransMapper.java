package com.daddylab.supplier.item.common.trans;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ScalarTransMapper {

    ScalarTransMapper INSTANCE = Mappers.getMapper(ScalarTransMapper.class);

    default int bool2int(Boolean boolVal) {
        return boolVal != null && boolVal ? 1 : 0;
    }

    default boolean int2bool(Integer intVal) {
        return intVal != null && intVal > 0;
    }
}

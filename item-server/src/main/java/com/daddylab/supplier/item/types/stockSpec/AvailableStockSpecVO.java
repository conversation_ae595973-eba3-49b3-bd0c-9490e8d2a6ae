package com.daddylab.supplier.item.types.stockSpec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@Data
public class AvailableStockSpecVO {
    /** 商品SKU */
    @ApiModelProperty("商品SKU")
    String skuCode;
    /** 商品SPU */
    @ApiModelProperty("商品SPU")
    String spuCode;
    /** 商品SPU名称 */
    @ApiModelProperty("商品SPU名称")
    String spuName;
    /** 商品SKU名称 */
    @ApiModelProperty("商品SKU名称")
    String skuName;
    /** 仓库编码 */
    @ApiModelProperty("仓库编码")
    String warehouseNo;
    /** 仓库 */
    @ApiModelProperty("仓库")
    String warehouseName;
    /** 库存数量 */
    @ApiModelProperty("仓内库存")
    BigDecimal stockNum;
    /** 可用库存 */
    @ApiModelProperty("可用库存")
    BigDecimal availableStock;
    /** 缺陷 */
    @ApiModelProperty("是否残次品")
    Boolean defect;

    public AvailableStockSpecVO ofInventoryRatio(Integer ratio) {
        final BigDecimal radioPercent = new BigDecimal(ratio).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return ofInventoryRatio(radioPercent);
    }

    public AvailableStockSpecVO ofInventoryRatio(Integer ratio1, Integer ratio2) {
        final BigDecimal radioPercent = new BigDecimal(ratio1 * ratio2).divide(new BigDecimal(10000),
                2,
                RoundingMode.HALF_UP);
        return ofInventoryRatio(radioPercent);
    }

    public AvailableStockSpecVO ofInventoryRatio(BigDecimal radio) {
        final AvailableStockSpecVO availableStockSpecVO = new AvailableStockSpecVO();
        availableStockSpecVO.setSkuCode(getSkuCode());
        availableStockSpecVO.setSpuCode(getSpuCode());
        availableStockSpecVO.setSpuName(getSpuName());
        availableStockSpecVO.setSkuName(getSkuName());
        availableStockSpecVO.setWarehouseNo(getWarehouseNo());
        availableStockSpecVO.setWarehouseName(getWarehouseName());
        availableStockSpecVO.setStockNum(getStockNum().multiply(radio));
        availableStockSpecVO.setAvailableStock(getAvailableStock().multiply(radio));
        availableStockSpecVO.setDefect(getDefect());

        return availableStockSpecVO;
    }


}

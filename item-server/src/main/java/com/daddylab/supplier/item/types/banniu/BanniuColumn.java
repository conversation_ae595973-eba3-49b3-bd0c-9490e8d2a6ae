package com.daddylab.supplier.item.types.banniu;


import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2023/9/1
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Documented
@Inherited
public @interface BanniuColumn {
    /**
     * 班牛组件列名
     */
    String value();

    /**
     * 描述
     */
    String desc() default "";

    /**
     * 是否必填
     */
    boolean required() default true;
}

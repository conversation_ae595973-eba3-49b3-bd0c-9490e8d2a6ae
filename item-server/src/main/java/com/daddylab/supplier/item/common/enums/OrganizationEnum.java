package com.daddylab.supplier.item.common.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购组织
 * <AUTHOR> up
 * @date 2022/3/29 5:40 下午
 */
@Getter
@AllArgsConstructor
public enum OrganizationEnum implements IEnum<Long> {

    /**
     *
     */
    DADDY_LAB(1L, "老爸评测"),
    MOTHER_LAB(2L, "老妈评测"),
    GRANDPA_LAB(3L, "爷爷评测"),
    GRANDMOTHER_LAB(4L, "奶奶评测");

    private final Long value;
    private final String desc;
}

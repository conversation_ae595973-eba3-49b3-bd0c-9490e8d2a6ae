package com.daddylab.supplier.item.types.stockSpec;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/7/19
 */
@Data
public class ShopAvailableStockSpecVO {
    /**
     * 店铺库存配置ID
     */
    private Long shopInventoryId;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺编码
     */
    private String shopNo;
    /**
     * 店铺库存模式
     */
    private InventoryMode shopInventoryMode;
    /**
     * 仓库编码
     */
    private String warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 是否为虚拟仓
     */
    private Boolean isVirtualWarehouse;
    /**
     * 仓库库存模式
     */
    private InventoryMode warehouseInventoryMode;
    /**
     * 虚拟仓下实仓的编码
     */
    private String realWarehouseNo;
    /**
     * 虚拟仓下实仓的名称
     */
    private String realWarehouseName;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品SKU
     */
    private String specNo;
    /**
     * 是否正品 0:正品 1:残次品
     */
    private Boolean defect;
    /**
     * 店铺可用库存
     */
    private BigDecimal shopAvailableStock;
}

package com.daddylab.supplier.item.application.platformItem;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemSyncStockCmd;
import com.daddylab.supplier.item.application.platformItem.data.SwitchExplicitlySyncCmd;
import com.daddylab.supplier.item.application.platformItem.data.SyncLogQuery;
import com.daddylab.supplier.item.application.platformItem.data.SyncLogVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockSyncRecord;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
public interface PlatformItemStockSyncBizService {
    /**
     * 同步平台商品库存
     *
     * @param id 平台商品ID（外）
     */
    Response syncStock(PlatformItemSyncStockCmd cmd);

    Response syncStock(Long platformItemId);

    PageResponse<SyncLogVO> syncLog(SyncLogQuery query);

    void putSyncLogAsync(StockSyncRecord record);

    Response explicitlySync(SwitchExplicitlySyncCmd cmd);
}

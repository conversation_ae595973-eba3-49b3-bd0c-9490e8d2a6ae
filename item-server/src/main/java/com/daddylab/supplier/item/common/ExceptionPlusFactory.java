package com.daddylab.supplier.item.common;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.ExceptionFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 5:59 下午
 * @description
 */
public class ExceptionPlusFactory extends ExceptionFactory {

    public static BizException bizException(ErrorCode errorCode) {
        return new BizException(errorCode.getCode(), errorCode.getMsg());
    }

    public static BizException bizException(ErrorCode errorCode, String errorMessage) {
        return new BizException(errorCode.getCode(),
                errorMessage != null ? errorMessage : errorCode.getMsg());
    }

    public static BizException bizException(ErrorCode errorCode, Response cause) {
        return new BizException(errorCode.getCode(),
                errorCode.getMsg() + "，错误原因:" + cause.getErrMessage() + "(" + cause.getErrCode()
                        + ")");
    }


}

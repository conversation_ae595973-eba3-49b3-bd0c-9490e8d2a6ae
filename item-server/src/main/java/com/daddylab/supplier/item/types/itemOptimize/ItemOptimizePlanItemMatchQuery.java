package com.daddylab.supplier.item.types.itemOptimize;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/10/22
 */
@Data
public class ItemOptimizePlanItemMatchQuery {
    @ApiModelProperty("平台商品ID")
    String id;

    @ApiModelProperty("平台商品链接 与平台商品ID不能同时为空")
    String link;

    @ApiModelProperty(
            value = "平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城",
            allowableValues = "1,2,3,4,5,6,7")
    @NotNull
    Integer platform;

    @ApiModelProperty("商品SPU编码 （非必填）若用户认为匹配到的商品编码不正确，可以手动指定后重新匹配")
    String itemCode;
}

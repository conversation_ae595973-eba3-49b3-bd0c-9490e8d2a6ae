package com.daddylab.supplier.item.domain.item.event;

import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditBizService;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/3/6
 */
@EventBusListener
@Service
@RequiredArgsConstructor
public class ItemQcChangeListener {

    private final ItemDrawerAuditBizService itemDrawerAuditBizService;

    @Subscribe
    public void onQcChange(ItemQcChangeEvent event) {
        itemDrawerAuditBizService.qcChange(event.getItemId(), event.getNewQcIds());
    }
}

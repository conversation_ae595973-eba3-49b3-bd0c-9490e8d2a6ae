package com.daddylab.supplier.item.types.process;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/12
 */
@Data
public class ProcessActivity {
    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("参与者")
    private List<StaffBrief> participants;

    @ApiModelProperty("活动开始时间")
    private Long startTime;

    @ApiModelProperty("活动结束时间")
    private Long endTime;

    @ApiModelProperty("备注")
    private String comment;
}

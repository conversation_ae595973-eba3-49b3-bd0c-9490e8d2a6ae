package com.daddylab.supplier.item.domain.platformItem.service;

import com.alibaba.nacos.shaded.com.google.common.eventbus.Subscribe;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.saleItem.service.SaleItemLibraryBizService;
import com.daddylab.supplier.item.domain.platformItem.service.event.PlatformItemUpdateEvent;
import com.daddylab.supplier.item.domain.platformItem.service.event.SyncSaleItemLibraryEvent;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Service
@EventBusListener
public class PlatformItemUpdateListener {
    @Autowired
    private ItemBizService itemBizService;
    @Autowired
    private SaleItemLibraryBizService saleItemLibraryBizService;

    @Subscribe
    public void onUpdate(PlatformItemUpdateEvent event) {

        itemListen(event);

        syncSaleItemLibraryListen(event);
    }

    private void itemListen(PlatformItemUpdateEvent event) {
        itemBizService.listenPlatformItemUpdate(event);
    }

    private void syncSaleItemLibraryListen(PlatformItemUpdateEvent event) {
        for (PlatformItemSku platformItemSkus : event.getPlatformItemSkus()) {
            final SyncSaleItemLibraryEvent syncSaleItemLibraryEvent = new SyncSaleItemLibraryEvent();
            syncSaleItemLibraryEvent.setGoodsName(event.getPlatformItem().getGoodsName());
            syncSaleItemLibraryEvent.setItemSkuCode(platformItemSkus.getSkuCode());

            saleItemLibraryBizService.synchronizeItemName(syncSaleItemLibraryEvent);
        }
    }

}

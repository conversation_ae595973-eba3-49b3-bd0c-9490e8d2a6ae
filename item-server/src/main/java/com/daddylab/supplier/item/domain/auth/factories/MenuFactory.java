package com.daddylab.supplier.item.domain.auth.factories;

import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.entity.SysResource;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.trans.AuthAssembler;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.tree.TreeFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/3/30
 */
public class MenuFactory {
    public static List<SysMenu> buildMenuList(long systemId,
                                              Map<ResourceType, List<String>> resourcesOwned,
                                              List<SysResource> menuResources) {
        return TreeFactory.buildList(menuResources, systemId,
                resource -> {
                    final SysMenu menu = AuthAssembler.INSTANCE.toMenu(resource);
                    final ResourceType resourceType = IEnum.getEnumByValue(ResourceType.class, menu.getType());
                    final List<String> codesThisType = resourcesOwned.get(resourceType);
                    if (codesThisType != null
                            && codesThisType.contains(menu.getFrontUrl())) {
                        menu.setHasPermission(true);
                    }
                    return menu;
                });
    }
}

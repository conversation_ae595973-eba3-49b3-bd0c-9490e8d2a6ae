package com.daddylab.supplier.item.common.trans;

import cn.hutool.core.util.ReUtil;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/22
 */
@Mapper
public interface StaffAssembler {
    StaffAssembler INST = Mappers.getMapper(StaffAssembler.class);

    default Long staffBriefToLong(StaffBrief staffBrief) {
        return staffBrief.getUserId();
    }

    default StaffBrief toStaffBrief(Long id) {
        if (!NumberUtil.isPositive(id)) {
            return StaffBrief.systemUser();
        }
        return ApplicationContextUtil.getBean(StaffService.class)
                                     .getStaffBrief(id, true)
                                     .orElse(null);
    }

    default StaffBrief toStaffBrief(String nickname) {
        if (StringUtil.isBlank(nickname)) {
            return null;
        }
        if (ReUtil.isMatch("^\\d+$", nickname)) {
            return toStaffBrief(Long.parseLong(nickname));
        }
        return ApplicationContextUtil.getBean(StaffService.class)
                                     .getStaffByNickName(nickname)
                                     .orElse(null);
    }

    default Collection<StaffBrief> nicknameList2staffList(Collection<String> nicknames) {
        if (nicknames == null || nicknames.isEmpty()) {
            return Collections.emptyList();
        }
        final StaffService bean = ApplicationContextUtil.getBean(StaffService.class);
        return bean.getStaffListByNickName(nicknames);
    }

    default List<StaffBrief> longListToStaffBriefList(List<Long> idList, boolean nullToEmptyList) {
        if (idList == null) {
            if (nullToEmptyList) {
                return Collections.emptyList();
            } else {
                return null;
            }
        }
        final StaffService bean = ApplicationContextUtil.getBean(StaffService.class);
        return idList.stream()
                     .filter(NumberUtil::isPositive)
                     .map(
                             id ->
                                     bean.getStaffBrief(id, true)
                                         .orElseThrow(
                                                 () ->
                                                         ExceptionPlusFactory.bizException(
                                                                 ErrorCode.DATA_NOT_FOUND,
                                                                 "当前实体关联用户信息查询异常，请联系系统管理员")))
                     .collect(Collectors.toList());
    }

    default List<StaffBrief> longListToStaffBriefList(List<Long> idList) {
        return longListToStaffBriefList(idList, true);
    }

    StaffBrief dadStaffVoToStaffBrief(DadStaffVO dadStaffVO);

    List<StaffBrief> dadStaffVoListToStaffBriefList(List<DadStaffVO> dadStaffVOs);
}

package com.daddylab.supplier.item.common.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchaseTypeEnum implements IEnum<Integer>{

    PURCHASE_ORDER(0, "采购订单"),
    IN_STOCK_PAYABLE (1, "采购入库应付"),
    OUT_STOCK_PAYABLE (2, "采退出库应付"),
    OTHER_PAYABLE (3, "其他应付"),
    SYSTEM_PURCHASE_ORDER (4, "采购订单--系统生成"),
    ITEM_LIBRARY_AUDIT(5, "商品库审核"),

    ;
    private final Integer value;
    private final String desc;


    public static String getNameByCode(Integer code) {
        for (PurchaseTypeEnum purchaseTypeEnum : PurchaseTypeEnum.values()) {
            if (purchaseTypeEnum.getValue().equals(code)) {
                return purchaseTypeEnum.getDesc();
            }
        }
        return null;
    }


    public static PurchaseTypeEnum getByCode(Integer code) {
        for (PurchaseTypeEnum purchaseTypeEnum : PurchaseTypeEnum.values()) {
            if (purchaseTypeEnum.getValue().equals(code)) {
                return purchaseTypeEnum;
            }
        }
        return null;
    }
}

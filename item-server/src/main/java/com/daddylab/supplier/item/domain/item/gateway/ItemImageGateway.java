package com.daddylab.supplier.item.domain.item.gateway;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 2:05 下午
 * @description
 */
public interface ItemImageGateway {

    /**
     * 查询 商品 图片
     *
     * @param itemId        商品id
     * @param itemImageType 商品图片类型
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage
     */
    List<ItemImage> getImageListByType(Long itemId, ItemImageType itemImageType);

    /**
     * 批量查询商品图片
     *
     * @param itemIds       商品IDs
     * @param itemImageType 商品图片类型
     * @return 商品图片
     */
    Map<Long, List<ItemImage>> batchGetItemImagesByType(List<Long> itemIds,
            ItemImageType itemImageType);

    /**
     * 批量查询商品主图
     *
     * @param itemIds 商品IDs
     * @return 商品图片
     */
    Map<Long, String> batchGetItemMainImgUrls(Collection<Long> itemIds);

    /**
     * 查询商品主图
     */
    String getItemMainImgUrl(Long itemId);

    List<ItemImage> getRunningImages(Long itemId);

    List<ItemImage> getItemImages(Long itemId);

    /**
     * 批量保存或者更新图片
     *
     * @param list com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage
     */
    void saveOrUpdateBatchImage(List<ItemImage> list);

    void removeImages(List<Long> itemImageIds);

}

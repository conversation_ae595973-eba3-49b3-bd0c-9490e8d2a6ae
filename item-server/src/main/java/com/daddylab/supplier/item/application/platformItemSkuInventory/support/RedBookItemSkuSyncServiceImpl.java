package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import cn.hutool.core.util.BooleanUtil;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.SkuPageQuery;
import com.daddylab.supplier.item.infrastructure.third.redbook.impl.RedBookServiceFactory;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetItemInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.ItemDetail;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.SkuDetail;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class RedBookItemSyncServiceImpl.java
 * @description 小红书数据处理类
 * @date 2024-03-14 09:29
 */
@Slf4j
@Service
public class RedBookItemSkuSyncServiceImpl extends AbstractPlatformItemSkuSync<RedBookItemSkuSyncServiceImpl.ProductItemWrapper, PlatformSkuSyncInfo> {
    @Autowired
    private RedBookServiceFactory redBookServiceFactory;

    @Override
    public void fullDoseSync() {
        final ArrayList<Step> steps = new ArrayList<>();
        for (ShopAuthorization shopAuthorization : shopAuthorizationService.listNotExpiredAuthorizations(defaultType())) {
            final Step step = buildStep(getFullDoseItemReader(shopAuthorization));
            steps.add(step);
        }
        steps.add(getStatisticsStep());
        run(steps.toArray(new Step[0]));
    }

    /**
     * 构建群全量查询ItemReader
     *
     * @return org.springframework.batch.item.ItemReader<com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse.Product>
     * @date 2024/3/14 11:55
     * <AUTHOR>
     */
    public ItemReader<ProductItemWrapper> getFullDoseItemReader(ShopAuthorization shopAuthorization) {
        return getItemReader(iPage -> {
            GetDetailSkuListResponse skuListResponse = redBookServiceFactory.getService(shopAuthorization)
                                                                            .getSkuPageList(SkuPageQuery.of((int) iPage.getCurrent(),
                                                                                    (int) iPage.getSize()));
            if (skuListResponse == null || skuListResponse.getData() == null) {
                return null;
            }
            final List<ProductItemWrapper> products = skuListResponse.getData().stream().map(product -> {
                final ProductItemWrapper productItemWrapper = new ProductItemWrapper();
                productItemWrapper.setProduct(product);
                productItemWrapper.setShopNo(shopAuthorization.getSn());
                return productItemWrapper;
            }).collect(Collectors.toList());

            iPage.setRecords(products);
            iPage.setTotal(skuListResponse.getTotal());
            return iPage;
        }, 100);
    }

    @Data
    public static class ProductItemWrapper {
        private GetDetailSkuListResponse.Product product;
        private String shopNo;
    }

    /**
     * 数据结构转化
     *
     * @return org.springframework.batch.item.ItemProcessor<com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse.Product, com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformItemSkuInventoryParams>
     * @date 2024/3/14 11:43
     * <AUTHOR>
     */
    @Override
    public ItemProcessor<ProductItemWrapper, PlatformSkuSyncInfo> getItemProcess() {
        return this::productToPlatformItemSkuInventoryParams;
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public ItemWriter<PlatformSkuSyncInfo> getItemWriter() {
        return platformParams -> saveSkuSyncInfo((List<PlatformSkuSyncInfo>) platformParams);
    }

    @Override
    public Platform defaultType() {
        return Platform.XIAOHONGSHU;
    }

    /**
     * 类型转换
     *
     * @param productItemWrapper Product
     * @return com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformItemSkuInventoryParams
     * @date 2024/3/14 13:53
     * <AUTHOR>
     */
    PlatformSkuSyncInfo productToPlatformItemSkuInventoryParams(ProductItemWrapper productItemWrapper) {
        final GetDetailSkuListResponse.Product product = productItemWrapper.getProduct();
        PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();
        platformSkuSyncInfo.setPlatform(defaultType());
        platformSkuSyncInfo.setShopNo(productItemWrapper.getShopNo());
        platformSkuSyncInfo.setOuterSkuId(product.getSku().getId());
        platformSkuSyncInfo.setOuterItemId(product.getSku().getItemId());
        platformSkuSyncInfo.setPrice(BigDecimal.valueOf(product.getSku().getPrice())
                                               .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        platformSkuSyncInfo.setStock(product.getSku().getStock().longValue());
        platformSkuSyncInfo.setStatus(BooleanUtil.toInt(product.getSku().isBuyable()));
        platformSkuSyncInfo.setOuterSkuCode(product.getSku().getErpCode());
        platformSkuSyncInfo.setOuterItemCode(null);
        platformSkuSyncInfo.setItemCreateTime(product.getItem().getCreateTime());
        platformSkuSyncInfo.setItemUpdateTime(product.getItem().getUpdateTime());
        platformSkuSyncInfo.setSkuCreateTime(product.getSku().getCreateTime());
        platformSkuSyncInfo.setSkuUpdateTime(product.getSku().getUpdateTime());
        platformSkuSyncInfo.setGoodsName(product.getItem().getName());
        platformSkuSyncInfo.setSpecName(product.getSku().getName());
        platformSkuSyncInfo.setSkuNum(null);
        return platformSkuSyncInfo;
    }

    @Override
    protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
        final GetItemInfoResponse itemInfoResponse = redBookServiceFactory.getService(shopNo).getItemInfo(itemId);

        return toPlatformSkuSyncInfos(
                shopNo,
                itemInfoResponse);
    }

    @NonNull
    private ArrayList<PlatformSkuSyncInfo> toPlatformSkuSyncInfos(String shopNo, GetItemInfoResponse itemInfoResponse) {
        final ItemDetail itemInfo = itemInfoResponse.getItemInfo();
        final List<SkuDetail> skuInfos = itemInfoResponse.getSkuInfos();
        final ArrayList<PlatformSkuSyncInfo> platformSkuSyncInfos = new ArrayList<>();
        for (SkuDetail skuInfo : skuInfos) {
            PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();
            platformSkuSyncInfo.setPlatform(defaultType());
            platformSkuSyncInfo.setShopNo(shopNo);
            platformSkuSyncInfo.setOuterSkuId(skuInfo.getId());
            platformSkuSyncInfo.setOuterItemId(skuInfo.getItemId());
            platformSkuSyncInfo.setStock(Long.valueOf(skuInfo.getStock()));
            platformSkuSyncInfo.setStatus(BooleanUtil.toInt(skuInfo.isBuyable()));
            platformSkuSyncInfo.setOuterSkuCode(skuInfo.getErpCode());
            platformSkuSyncInfo.setOuterItemCode(null);
            platformSkuSyncInfo.setItemCreateTime(itemInfo.getCreateTime());
            platformSkuSyncInfo.setItemUpdateTime(itemInfo.getUpdateTime());
            platformSkuSyncInfo.setSkuCreateTime(skuInfo.getCreateTime());
            platformSkuSyncInfo.setSkuUpdateTime(skuInfo.getUpdateTime());
            platformSkuSyncInfo.setGoodsName(itemInfo.getName());
            platformSkuSyncInfo.setSpecName(skuInfo.getName());
            platformSkuSyncInfo.setSkuNum(skuInfos.size());
            platformSkuSyncInfo.setPrice(new BigDecimal(skuInfo.getPrice())
                    .divide(new BigDecimal(10), 2, RoundingMode.UNNECESSARY));
            platformSkuSyncInfos.add(platformSkuSyncInfo);
        }
        return platformSkuSyncInfos;
    }

    @Override
    public void incrementSync(LocalDateTime startTime, LocalDateTime endTime) throws UnsupportedOperationException {
        for (ShopAuthorization shopAuthorization : shopAuthorizationService.listNotExpiredAuthorizations(defaultType())) {
            final SkuPageQuery pageQuery = new SkuPageQuery();
            pageQuery.setUpdateTimeFrom(startTime.atZone(ZoneId.systemDefault()).toEpochSecond());
            pageQuery.setUpdateTimeTo(endTime.atZone(ZoneId.systemDefault()).toEpochSecond());
            final GetDetailSkuListResponse skuPageList = redBookServiceFactory.getService(shopAuthorization)
                                                                              .getSkuPageList(pageQuery);
            if (skuPageList != null && skuPageList.getData() != null) {
                final List<PlatformSkuSyncInfo> skuSyncInfo = skuPageList.getData().stream().map(v -> {
                    final ProductItemWrapper productItemWrapper = new ProductItemWrapper();
                    productItemWrapper.setProduct(v);
                    productItemWrapper.setShopNo(shopAuthorization.getSn());
                    return productItemWrapper;
                }).map(this::productToPlatformItemSkuInventoryParams).collect(Collectors.toList());
                saveSkuSyncInfo(skuSyncInfo);
            }
        }
    }
}

package com.daddylab.supplier.item.types.recognitionTask;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 状态 0 失败 1 准备 2 等待敏感词检查 3 敏感词检测完成
 *
 * <AUTHOR>
 * @since 2022/12/5
 */
@RequiredArgsConstructor
@Getter
public enum ItemDrawerRecognitionTaskStatus implements IIntegerEnum {

    FAIL(0, "失败"),
    PREPARE(1, "准备中"),
    WAIT_CHECK(2, "等待敏感词检查"),
    FINISHED(3, "敏感词检测完成"),
    ;

    private final Integer value;
    private final String desc;
}

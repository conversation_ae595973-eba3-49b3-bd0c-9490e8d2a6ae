package com.daddylab.supplier.item.types.refundOrder;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
@Data
@ApiModel("退换单基础信息（P系统）")
public class RefundOrderBaseInfoForP {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    Long id;

    /**
     * 退款单号
     **/
    @ApiModelProperty("退款单号")
    String refundOrderNo;

    /**
     * 原始订单号
     **/
    @ApiModelProperty("原始订单号")
    String srcOrderNos;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    Long createTime;

    /**
     * 售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款
     **/
    @ApiModelProperty("售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款")
    Integer type;

    /**
     * 退换单商品明细
     */
    @ApiModelProperty("退换单商品明细")
    List<RefundOrderItem> itemDetails;

    @ApiModelProperty("退货仓库编号")
    String returnWarehouseNo;

    @ApiModelProperty("退货仓库名称")
    String returnWarehouseName;

    @ApiModelProperty("退回物流")
    String returnLogisticsName;

    @ApiModelProperty("退回物流单号")
    String returnLogisticsNo;

    @ApiModelProperty("收货状态：WAIT 待收货 RECEIVED 已收货 CONFIRMED 已确认（仅退货、换货类型售后有此状态，其他类型此字段为空")
    AfterSalesReceiveState receiveState;

    @ApiModelProperty("是否是当前供应商的单据")
    Boolean selfOrder;

    @ApiModelProperty("是否可以进行转寄操作")
    Boolean canSendOn;

}

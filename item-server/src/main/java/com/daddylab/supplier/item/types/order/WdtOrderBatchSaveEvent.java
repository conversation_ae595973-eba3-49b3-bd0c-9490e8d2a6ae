package com.daddylab.supplier.item.types.order;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class WdtOrderBatchSaveEvent {
    @Getter
    private Collection<WdtOrder> models;
}

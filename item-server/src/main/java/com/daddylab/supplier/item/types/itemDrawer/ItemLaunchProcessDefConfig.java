package com.daddylab.supplier.item.types.itemDrawer;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */
@RefreshScope
@ConfigurationProperties(prefix = "item-launch-process-def")
@Configuration
@Data
public class ItemLaunchProcessDefConfig {

    private List<Long> legalIds;
}

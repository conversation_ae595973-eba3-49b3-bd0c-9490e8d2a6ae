package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibraryCmd;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibrarySheet;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SaleItemLibrary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SaleItemLibraryTransMapper {

    SaleItemLibraryTransMapper INSTANCE = Mappers.getMapper(SaleItemLibraryTransMapper.class);

    SaleItemLibrary cmdToDo(SaleItemLibraryCmd cmd);

    SaleItemLibrarySheet cmdToSheet(SaleItemLibraryCmd cmd);

    @Mapping(target = "id", source = "cmd.id")
    @Mapping(target = "itemSkuCode", source = "cmd.itemSkuCode")
    @Mapping(target = "platformPrice", source = "cmd.platformPrice")
    @Mapping(target = "platformActivityContent", source = "cmd.platformActivityContent")
    @Mapping(target = "platformActivityCode", source = "cmd.platformActivityCode")
    @Mapping(target = "platformCost", source = "cmd.platformCost")
    @Mapping(target = "remark", source = "cmd.remark")
    @Mapping(target = "category", source = "cmd.category")
    SaleItemLibrary voToDo(SaleItemLibraryVO vo, SaleItemLibraryCmd cmd);

    List<SaleItemLibrary> listSheetCmdToDo(List<SaleItemLibrarySheet> sheetList);

    List<SaleItemLibrarySheet> listVoToSheet(List<SaleItemLibraryVO> list);
}


package com.daddylab.supplier.item.types.warehouse;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 仓库售后退回地址
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EditWarehouseAfterSalesAddressCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 仓库编号 */
    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    /** 退回地址 */
    @ApiModelProperty("退回地址")
    private String fullAddress = "";

    /** 退货联系人 */
    @ApiModelProperty("退货联系人")
    private String contacts = "";

    /** 退货联系电话 */
    @ApiModelProperty("退货联系电话")
    private String tel = "";

    public static EditWarehouseAfterSalesAddressCmd of(EditWarehouseCmd editWarehouseCmd) {
        final EditWarehouseAfterSalesAddressCmd editWarehouseAfterSalesAddressCmd =
                new EditWarehouseAfterSalesAddressCmd();
        editWarehouseAfterSalesAddressCmd.setWarehouseNo(editWarehouseCmd.getWarehouseNo());
        editWarehouseAfterSalesAddressCmd.setFullAddress(editWarehouseCmd.getFullAddress());
        editWarehouseAfterSalesAddressCmd.setContacts(editWarehouseCmd.getContacts());
        editWarehouseAfterSalesAddressCmd.setTel(editWarehouseCmd.getTel());
        return editWarehouseAfterSalesAddressCmd;
    }
}

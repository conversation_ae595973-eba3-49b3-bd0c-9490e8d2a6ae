package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableCmd;
import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableMapper.java
 * @description
 * @createTime 2021年11月16日 21:25:00
 */
@Mapper
public interface PurchaseTableTransMapper {

    PurchaseTableTransMapper INSTANCE = Mappers.getMapper(PurchaseTableTransMapper.class);

    PurchaseTableVo doToVo(PurchaseTable purchaseTable);

    PurchaseTable cmdToDo(PurchaseTableCmd cmd);

}

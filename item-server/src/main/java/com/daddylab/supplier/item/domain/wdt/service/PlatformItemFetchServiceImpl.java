package com.daddylab.supplier.item.domain.wdt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchResponse;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtPlatformGoodsMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/1/6
 */
@Slf4j
@Service
public class PlatformItemFetchServiceImpl implements PlatformItemFetchService {

    final GoodsAPI goodsAPI;
    final GoodsAPI qimenGoodsAPI;
    final private WdtPlatformGoodsMapper wdtPlatformGoodsMapper;
    final private ShopGateway shopGateway;
    final private RefreshConfig refreshConfig;

    @Autowired
    public PlatformItemFetchServiceImpl(WdtGateway wdtGateway,
            WdtPlatformGoodsMapper wdtPlatformGoodsMapper,
            ShopGateway shopGateway,
            RefreshConfig refreshConfig) {
        this.goodsAPI = wdtGateway.goodsAPI();
        this.qimenGoodsAPI = wdtGateway.qimenGoodsAPI();
        this.wdtPlatformGoodsMapper = wdtPlatformGoodsMapper;
        this.shopGateway = shopGateway;
        this.refreshConfig = refreshConfig;
    }

    /**
     * 拉取旺店通平台商品数据，落地到数据库
     *
     * @param startTime 拉取时间范围-开始时间
     * @param endTime   拉取时间范围-结束时间
     * @param pageIndex pageIndex
     * @param pageSize  pageSize
     * @return 这一批数据的总量
     */
    @Override
    public int fetch(LocalDateTime startTime, LocalDateTime endTime, int pageIndex,
            int pageSize) throws WdtErpException {
        return fetch(startTime, endTime, pageIndex, pageSize, null, null, null);
    }

    /**
     * 统计指定时间段内的数据量
     *
     * @param startTime 拉取时间范围-开始时间
     * @param endTime   拉取时间范围-结束时间
     * @return
     * @throws WdtErpException
     */
    @Override
    public int count(LocalDateTime startTime, LocalDateTime endTime) throws WdtErpException {
        return query(startTime, endTime, 1, 1, null, null, null).getTotalCount();
    }

    /**
     * 拉取旺店通平台商品数据，落地到数据库
     *
     * @param startTime 拉取时间范围-开始时间
     * @param endTime   拉取时间范围-结束时间
     * @param pageIndex pageIndex
     * @param pageSize  pageSize
     * @param goodsId   外部平台商品ID
     * @param specId    外部平台商品规格ID
     * @param shopNo    旺店通店铺编号
     * @return 这一批数据的总量
     */
    @Override
    public int fetch(LocalDateTime startTime, LocalDateTime endTime, int pageIndex, int pageSize
            , String goodsId, String specId, String shopNo) throws WdtErpException {
        PlatformGoodsSearchResponse platformGoodsSearchResponse = query(
                startTime, endTime, pageIndex, pageSize, goodsId, specId, shopNo);
        handleFetchData(platformGoodsSearchResponse);
        return platformGoodsSearchResponse.getTotalCount();
    }

    private PlatformGoodsSearchResponse query(LocalDateTime startTime,
            LocalDateTime endTime, int pageIndex, int pageSize, String goodsId, String specId,
            String shopNo) throws WdtErpException {
        final DateTimeFormatter normDateTimeFormatter = DatePattern.NORM_DATETIME_FORMATTER;
        final PlatformGoodsSearchRequest platformGoodsSearchRequest = new PlatformGoodsSearchRequest();
        platformGoodsSearchRequest.setStartTime(startTime.format(normDateTimeFormatter));
        platformGoodsSearchRequest.setEndTime(endTime.format(normDateTimeFormatter));
        if (StringUtil.isNotBlank(goodsId)) {
            platformGoodsSearchRequest.setGoodsId(goodsId);
        }
        if (StringUtil.isNotBlank(specId)) {
            platformGoodsSearchRequest.setSpecId(specId);
        }
        if (StringUtil.isNotBlank(shopNo)) {
            platformGoodsSearchRequest.setShopNo(shopNo);
        }
        final Pager pager = new Pager(pageSize, pageIndex - 1, true);
        PlatformGoodsSearchResponse platformGoodsSearchResponse;
        if (Boolean.TRUE.equals(refreshConfig.getFetchWdtPlatformItemByQimen())) {
            platformGoodsSearchResponse = qimenGoodsAPI
                    .platformGoodsSearch(platformGoodsSearchRequest, pager);
        } else {
            platformGoodsSearchResponse = goodsAPI
                    .platformGoodsSearch(platformGoodsSearchRequest, pager);
        }
        return platformGoodsSearchResponse;
    }

    private void handleFetchData(PlatformGoodsSearchResponse platformGoodsSearchResponse) {
        final List<PlatformGoodsSearchResponse.GoodsListItem> goodsList = platformGoodsSearchResponse
                .getGoodsList();
        if (CollUtil.isEmpty(goodsList)) {
            return;
        }
        final List<String> shopNos = goodsList.stream()
                .map(PlatformGoodsSearchResponse.GoodsListItem::getShopNo)
                .filter(StringUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, Shop> shopMap = shopGateway.batchQueryShopBySn(shopNos);
        batchHandle(goodsList, shopMap);
    }

    private void batchHandle(List<PlatformGoodsSearchResponse.GoodsListItem> goodsList,
            Map<String, Shop> shopMap) {
        int batchSize = 100;
        final ArrayList<WdtPlatformGoods> buffers = Lists.newArrayList();
        for (PlatformGoodsSearchResponse.GoodsListItem goodsListItem : goodsList) {
            WdtPlatformGoods wdtPlatformGoods = toWdtPlatformGoods(goodsListItem, shopMap);
            buffers.add(wdtPlatformGoods);
            if (buffers.size() >= batchSize) {
                wdtPlatformGoodsMapper.saveOrUpdateBatch(buffers);
                buffers.clear();
            }
        }
        if (!buffers.isEmpty()) {
            wdtPlatformGoodsMapper.saveOrUpdateBatch(buffers);
            buffers.clear();
        }
    }

    @NonNull
    private WdtPlatformGoods toWdtPlatformGoods(
            PlatformGoodsSearchResponse.GoodsListItem goodsListItem,
            Map<String, Shop> shopMap) {
        WdtPlatformGoods wdtPlatformGoods = new WdtPlatformGoods();
        final String shopNo = goodsListItem.getShopNo();
        final Optional<Shop> shop = Optional.ofNullable(shopMap.get(shopNo));
        wdtPlatformGoods.setShopId(shop.map(Shop::getId).map(Long::intValue).orElse(0));
        wdtPlatformGoods.setShopNo(shopNo);
        wdtPlatformGoods.setShopName(goodsListItem.getShopName());
        wdtPlatformGoods.setGoodsName(goodsListItem.getGoodsName());
        wdtPlatformGoods.setSpecName(goodsListItem.getSpecName());
        wdtPlatformGoods.setSpecOuterId(goodsListItem.getSpecOuterId());
        wdtPlatformGoods.setOuterId(goodsListItem.getOuterId());
        wdtPlatformGoods.setGoodsId(goodsListItem.getGoodsId());
        wdtPlatformGoods.setSpecId(goodsListItem.getSpecId());
        wdtPlatformGoods.setRecId(String.valueOf(goodsListItem.getRecId()));
        wdtPlatformGoods.setIsDeleted(goodsListItem.getIsDeleted());
        wdtPlatformGoods.setPrice(BigDecimal.valueOf(goodsListItem.getPrice()));
        wdtPlatformGoods.setStockNum(BigDecimal.valueOf(goodsListItem.getStockNum()));
        wdtPlatformGoods.setStatus(goodsListItem.getStatus());
        wdtPlatformGoods.setIsAutoMatch(goodsListItem.getIsAutoMatch());
        wdtPlatformGoods.setMatchTargetType(goodsListItem.getMatchTargetType());
        wdtPlatformGoods.setHoldStock(BigDecimal.valueOf(goodsListItem.getHoldStock()));
        wdtPlatformGoods.setHoldStockType(goodsListItem.getHoldStockType());
        wdtPlatformGoods.setIsAutoListing(goodsListItem.getIsAutoMatch());
        wdtPlatformGoods.setIsAutoDelisting(goodsListItem.getIsAutoMatch());
        wdtPlatformGoods.setModified(DateUtil.parseCompatibility(goodsListItem.getModified()));
        wdtPlatformGoods.setMerchantNo(
                goodsListItem.getMerchantNo() != null ? goodsListItem.getMerchantNo() : "");
        wdtPlatformGoods.setMerchantName(goodsListItem.getMerchantName());
        wdtPlatformGoods.setMerchantCode(
                goodsListItem.getMerchantCode() != null ? goodsListItem.getMerchantCode() : "");
        wdtPlatformGoods.setCreatedAt(DateUtil.currentTime());
        wdtPlatformGoods.setCreatedUid(0L);
        wdtPlatformGoods.setUpdatedAt(DateUtil.currentTime());
        wdtPlatformGoods.setUpdatedUid(0L);
        return wdtPlatformGoods;
    }
}

package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.purchasePayable.dto.PurchasePayableCmd;
import com.daddylab.supplier.item.application.purchasePayable.vo.PurchasePayableVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchasePayableTransMapper {

    PurchasePayableTransMapper INSTANCE = Mappers.getMapper(PurchasePayableTransMapper.class);

    PurchasePayableVO doToVO(PurchasePayableOrder purchasePayableOrder);

    List<PurchasePayableVO> listToVO(List<PurchasePayableOrder> list);

    PurchasePayableOrder cmdToDO(PurchasePayableCmd cmd);

    PurchasePayableCmd eventToCmd(StockInOrOutEvent stockInOrOutEvent);
}

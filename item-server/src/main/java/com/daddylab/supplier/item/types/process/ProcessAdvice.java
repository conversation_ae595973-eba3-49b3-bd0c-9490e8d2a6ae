package com.daddylab.supplier.item.types.process;

import com.daddylab.supplier.item.infrastructure.enums.IStringEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@RequiredArgsConstructor
@Getter
public enum ProcessAdvice implements IStringEnum {
    NONE("none", "未给出意见"),
    AGREE("agree", "同意"),
    REFUSE("refuse", "拒绝"),
    ;

    private final String value;
    private final String desc;

}

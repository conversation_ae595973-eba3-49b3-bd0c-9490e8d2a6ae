package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.common.dto.WarehousePageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 仓库列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
public interface IWarehouseService extends IDaddyService<Warehouse> {


    Page<Warehouse> pageQuery(WarehousePageQuery query);

    Optional<Warehouse> queryWarehouseByNo(String warehouseNo);

    /**
     * 查询 编码 名称、
     *
     * @param noList
     * @return
     */
    List<WarehouseDO> queryWarehouseMapByNo(Collection<String> noList);

    Optional<Warehouse> queryBySkuCodeAndIsGift(String skuCode, Integer isGift, BigDecimal taxPrice);

    List<String> getOrderPersonnelNickName(String warehouseNo);

    Map<String, List<String>> getOrderPersonnelNickName(Collection<String> warehouseNos);

    List<String> getWarehouseNoByOrderPersonnel(List<Long> orderPersonnelIds);

    Map<String, String> getNameAndNo(Collection<String> nos);

    Collection<String> warehouseIdsToNos(List<Long> warehouseIds);

    Map<Long, String> warehouseIdsToNosMap(List<Long> warehouseIds);

    List<Warehouse> getBatchByNos(List<String> warehouseNos);

    /**
     * 页面展示为之前累计的库存数据。所以这边针对编辑的情况，需要用原有的库存减去待编辑的旧值
     *
     * @param warehouseNo
     * @param oldUpdateRatio
     * @return
     */
    Integer remainWarehouseRatio(String warehouseNo, Integer oldUpdateRatio);

    List<Warehouse> listByName(String warehouseName);
}

package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2025年06月18日 5:38 PM
 */
@FeignClient(name = "BpmFeignClient", url = "${bpm.url}")
public interface BpmFeignClient {

  @GetMapping(
      value = "/token/{oaUser}/{oaPwd}",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  String token(
      @PathVariable("oaUser") String oaUser,
      @PathVariable("oaPwd") String oaPwd,
      @RequestParam("loginName") String loginName);

  @PostMapping(value = "/attachment", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  String uploadAttachment(
      @RequestParam("token") String token, @RequestPart("file") MultipartFile file);

  @PostMapping(value = "/bpm/process/start", consumes = MediaType.APPLICATION_JSON_VALUE)
  String pushForm(String reqJson, @RequestHeader("token") String token);
}
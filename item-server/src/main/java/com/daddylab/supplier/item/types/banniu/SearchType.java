package com.daddylab.supplier.item.types.banniu;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/11/8
 */
@Getter
@AllArgsConstructor
public enum SearchType implements IIntegerEnum {
    EQ(1, "等于"),
    CONTAIN_TEXT(3, "包含(文本组件)"),
    CONTAIN_ITEM(3, "包含任一项(下拉组件)"),
    ;

    final Integer value;
    final String desc;

    @JsonValue
    @Override
    public String toString() {
        return value.toString();
    }
}

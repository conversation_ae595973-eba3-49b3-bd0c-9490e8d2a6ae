package com.daddylab.supplier.item.types;

import lombok.ToString;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2024/3/4
 */
@ToString
public class BatchResultStat {
    private final AtomicInteger newObject = new AtomicInteger();

    public void addNewObject() {
        newObject.incrementAndGet();
    }

    public int getNewObject() {
        return newObject.intValue();
    }

    private final AtomicInteger updateObject = new AtomicInteger();

    public void addUpdateObject() {
        updateObject.incrementAndGet();
    }

    public int getUpdateObject() {
        return updateObject.intValue();
    }

    private final AtomicInteger removeObject = new AtomicInteger();

    public void addRemoveObject() {
        removeObject.incrementAndGet();
    }

    public int getRemoveObject() {
        return removeObject.intValue();
    }
}

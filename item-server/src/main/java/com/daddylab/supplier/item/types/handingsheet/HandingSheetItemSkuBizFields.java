package com.daddylab.supplier.item.types.handingsheet;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@Data
public class HandingSheetItemSkuBizFields {

    @ApiModelProperty("规格名称")
    private String specifications;

    @ApiModelProperty("所属平台")
    private List<Platform> platforms;

    @ApiModelProperty("商品类目（后端）")
    private String categoryPath;

    @ApiModelProperty("单件是否包邮")
    private Boolean isFreeShippingForSingleItem;

    @ApiModelProperty(value = "产品日销价/SKU原价")
    private BigDecimal dailyPriceOrOriginalPrice;

    @ApiModelProperty(value = "日常活动机制")
    private String dailyActivities;

    @ApiModelProperty(value = "S级活动价")
    @JsonProperty("sLevelActivePrice")
    private BigDecimal sLevelActivePrice;

    @ApiModelProperty(value = "S级活动机制")
    @JsonProperty("sLevelActiveContent")
    private String sLevelActiveContent;

    @ApiModelProperty(value = "S级直播价")
    @JsonProperty("sLevelPromoteLivePrice")
    private String sLevelPromoteLivePrice;

    @ApiModelProperty("S级直播机制")
    @JsonProperty("sLevelPromoteLiveRule")
    private String sLevelPromoteLiveRule;

    @ApiModelProperty("采购员")
    private StaffBrief buyer;

    @ApiModelProperty("采购成本")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "发货类型 1:工厂发货 2:仓库发货")
    private Integer shipmentType;

    @ApiModelProperty("备注")
    private String remark;
}

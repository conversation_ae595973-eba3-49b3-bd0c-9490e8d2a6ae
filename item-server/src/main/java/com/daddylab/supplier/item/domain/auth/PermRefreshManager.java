package com.daddylab.supplier.item.domain.auth;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 权限查询管理器，控制是否需要刷新权限（记录最后一次权限查询时间及最近一次权限刷新时间，查询时间在刷新时间之前就表明需要刷新权限）
 * <AUTHOR>
 * @since 2022/2/18
 */
@Component
public class PermRefreshManager {
    private final RBucket<LocalDateTime> lastRefreshBucket;

    public PermRefreshManager(RedissonClient redissonClient) {
        lastRefreshBucket = redissonClient.getBucket(AuthConstants.CK_LAST_PERMISSION_REFRESH);
    }

    public boolean isNeedRefreshPerm(LocalDateTime queryTime) {
        if (queryTime == null)
            return true;
        final Optional<LocalDateTime> lastRefreshTime = getLastRefreshTime();
        return lastRefreshTime.isPresent() && queryTime.isBefore(lastRefreshTime.get());
    }

    public Optional<LocalDateTime> getLastRefreshTime() {
        return Optional.ofNullable(lastRefreshBucket.get());
    }

    public void setLastRefreshTime(LocalDateTime lastRefreshTime) {
        lastRefreshBucket.set(lastRefreshTime);
    }

    public static PermRefreshManager getInstance() {
        return ApplicationContextUtil.getBean(PermRefreshManager.class);
    }
}

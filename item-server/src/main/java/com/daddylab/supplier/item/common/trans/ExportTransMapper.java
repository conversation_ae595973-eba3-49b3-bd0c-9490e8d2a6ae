package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseSheet;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 3:46 下午
 * @description
 */
@Mapper
public interface ExportTransMapper {

    ExportTransMapper INSTANCE = Mappers.getMapper(ExportTransMapper.class);

    ExportCmd itemExportCmdToCmd(ExportCmd cmd);

    CombinationItemPageQuery combinationItemExportQueryToQuery(CombinationItemPageQuery queryPage);

    PurchaseQueryPage purchaseQueryPageToQuery(PurchaseQueryPage queryPage);

    StockInOrderAndOrderDetailQuery stockInOrderQueryToQuery(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery);

    StockOutOrderQuery stockOutOrderQueryToQuery(StockOutOrderQuery query);

    PurchaseSheet voToSheet(PurchaseVo vo);
}

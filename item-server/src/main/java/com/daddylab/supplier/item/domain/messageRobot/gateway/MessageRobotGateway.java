package com.daddylab.supplier.item.domain.messageRobot.gateway;


import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.messageRobot.exceptions.MessageRobotException;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-04
 */
public interface MessageRobotGateway {

    boolean sendText(MessageRobotCode code, String content) throws MessageRobotException;

    boolean sendText(String code, String content) throws MessageRobotException;

    boolean sendMarkdown(MessageRobotCode code, String markdown) throws MessageRobotException;

    boolean sendMarkdown(String code, String markdown) throws MessageRobotException;
}

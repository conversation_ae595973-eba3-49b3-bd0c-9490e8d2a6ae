package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.controller.otherStockInDetail.dto.OtherStockInDetailVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockInDetailTransMapper.java
 * @description
 * @createTime 2022年04月01日 14:00:00
 */
@Mapper
public interface OtherStockInDetailTransMapper {
    OtherStockInDetailTransMapper INSTANCE = Mappers.getMapper(OtherStockInDetailTransMapper.class);

    List<OtherStockInDetailVo> otherDbToVos(List<OtherStockInDetail> otherStockInDetails);
}

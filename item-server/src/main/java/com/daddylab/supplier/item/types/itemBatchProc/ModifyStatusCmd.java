package com.daddylab.supplier.item.types.itemBatchProc;

import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */
@Data
@JsonInclude
public class ModifyStatusCmd {
  @ApiModelProperty(value = "商品查询条件", required = true)
  @NotNull
  @Valid
  private ItemPageQuery query;

  @ApiModelProperty(value = "商品状态 0:待上架 1:在售中 2:已下架（永久）3:废弃", required = true)
  @NotNull
  private Integer status;

  @ApiModelProperty("商品下架时间")
  private Long downFrameTime;

  @ApiModelProperty("商品下架原因")
  private String downFrameReason;
}

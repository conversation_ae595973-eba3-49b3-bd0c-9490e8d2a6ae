package com.daddylab.supplier.item.types.itemDrawer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
@Data
public class TaskCompleteCmd {

    @Positive(message = "任务ID不能为空")
    @ApiModelProperty("任务ID")
    Long taskId;

    @NotEmpty(message = "审批建议不能为空")
    @ApiModelProperty("审批意见")
    List<ItemDrawerModuleModifyAdviceForm> advices;

    @ApiModelProperty("通过还是拒绝")
    @NotNull
    Boolean pass = true;

    private Long liveVerbalTrickId;

    @ApiModelProperty("QC暂存建议。保存还是提交。false,保存数据，不做提交，不做状态改变")
    private Boolean submit;
}

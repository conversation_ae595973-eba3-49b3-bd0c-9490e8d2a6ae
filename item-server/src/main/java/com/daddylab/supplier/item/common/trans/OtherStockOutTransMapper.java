package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOut;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutTransMapper.java
 * @description
 * @createTime 2022年04月01日 17:40:00
 */
@Mapper
public interface OtherStockOutTransMapper {

    OtherStockOutTransMapper INSTANCE = Mappers.getMapper(OtherStockOutTransMapper.class);

    List<OtherStockOutVo> doToVos(List<OtherStockOut> otherStockOuts);

    OtherStockOutVo doToVo(OtherStockOut otherStockOut);
}

package com.daddylab.supplier.item.common.trans;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PageTransMapper {
    PageTransMapper INSTANCE = Mappers.getMapper(PageTransMapper.class);

    @SuppressWarnings({"rawtypes"})
    @Mappings({
            @Mapping(target = "errMessage", ignore = true),
            @Mapping(target = "errCode", ignore = true),
            @Mapping(source = "total", target = "totalCount"),
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageIndex"),
            @Mapping(source = "records", target = "data"),
            @Mapping(constant = "true", target = "success")
    })
    PageResponse toPageResponse(IPage page);
}

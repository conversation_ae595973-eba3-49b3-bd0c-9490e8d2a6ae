package com.daddylab.supplier.item.types.baseinfo;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
public class ResponsiblePersonQuery extends PageQuery {
    private static final long serialVersionUID = 8177662267920368517L;

    @ApiModelProperty("负责人类型 1 采购负责人 2 QC负责人 3 技术负责人 4 法务负责人")
    @NotNull
    Integer type;

    @ApiModelProperty("花名")
    String name;

    @ApiModelProperty("用户ID")
    Long userId;
}

package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItem.data.*;
import com.daddylab.supplier.item.application.third.impl.pdd.PddStockService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.CommonEnumTransMapper;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.ArkSailorItemBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianSkuStockParam;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianTemplate;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockSyncRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncDirection;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockSyncRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ErpItemSkuUpdateStockParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.enums.StockTypeEnum;
import com.daddylab.supplier.item.infrastructure.third.enums.StockChangeTypeEnum;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KuaiShouServiceFactory;
import com.daddylab.supplier.item.infrastructure.third.redbook.impl.RedBookServiceFactory;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchResponse;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StockSyncBizServiceImpl
        implements PlatformItemStockSyncBizService, InitializingBean, DisposableBean {
    private final IStockSyncRecordService stockSyncRecordService;
    private final RedBookServiceFactory redBookServiceFactory;
    private final DouDianTemplate douDianTemplate;
    private final KuaiShouServiceFactory kuaiShouServiceFactory;
    private final ArkSailorItemBizService arkSailorItemBizService;
    private final PlatformItemSyncConfig platformItemSyncConfig;
    private final PddStockService pddStockService;

    @Autowired private PlatformItemBizService platformItemBizService;

    @Autowired private IPlatformItemService platformItemService;

    private ScheduledExecutorService scheduledExecutorService;
    private final BlockingQueue<StockSyncRecord> queue = new LinkedBlockingQueue<>(1000);

    @Override
    public void afterPropertiesSet() throws Exception {
        scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutorService.scheduleAtFixedRate(this::flushQueue, 3, 10, TimeUnit.SECONDS);
    }

    @Override
    public void destroy() throws Exception {
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
        }
    }

    private void flushQueue() {
        if (!queue.isEmpty()) {
            ArrayList<StockSyncRecord> records = new ArrayList<>(queue.size());
            queue.drainTo(records);
            queue.clear();
            stockSyncRecordService.saveBatch(records);
        }
    }

    @Override
    public Response syncStock(PlatformItemSyncStockCmd cmd) {
        final Platform platform = cmd.getPlatform();
        final List<PlatformItem> platformItems =
                platformItemService
                        .lambdaQuery()
                        .eq(PlatformItem::getPlatform, platform)
                        .eq(PlatformItem::getOuterItemId, cmd.getOuterItemId())
                        .list();
        for (PlatformItem platformItem : platformItems) {
            syncStock(platformItem);
        }
        return Response.buildSuccess();
    }

    @Override
    public Response syncStock(Long platformItemId) {
        final PlatformItem platformItem = platformItemService.getById(platformItemId);
        Assert.notNull(platformItem, "无效的平台商品");
        return syncStock(platformItem);
    }

    @Autowired private IShopAuthorizationService shopAuthorizationService;

    @NonNull
    private Response syncStock(PlatformItem platformItem) {
        if (!platformItem.getSyncEnabled()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.PLATFORM_ITEM_STOCK_SYNC_ERROR,
                    "商品未开启库存同步");
        }
        final List<PlatformItemDetailSku> platformItemDetailSkuList =
                platformItemBizService.detailSkuList(platformItem.getId());
        if (platformItemDetailSkuList.isEmpty()) {
            return Response.buildSuccess();
        }
        final Platform platform = platformItem.getPlatform();
        if (platformItem.getStatus() != PlatformItemStatus.ON_SALE) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.PLATFORM_ITEM_STOCK_SYNC_ERROR, "平台商品当前状态非在售，无法同步库存");
        }
        final Optional<PlatformItemSyncConfig.ConfigItem> testConfig =
                platformItemSyncConfig.getTestConfig(platform);
        final String outerItemId =
                testConfig
                        .map(PlatformItemSyncConfig.ConfigItem::getItemId)
                        .orElse(platformItem.getOuterItemId());
        Predicate<PlatformItemDetailSku> skuFilter = this::getSkuFilter;
        switch (platform) {
            case DOUDIAN:
                updateStockThenRecord(
                        platformItem,
                        platformItemDetailSkuList,
                        () -> {
                            final ArrayList<DouDianSkuStockParam> paramList = new ArrayList<>();
                            for (PlatformItemDetailSku sku : platformItemDetailSkuList) {
                                if (!skuFilter.test(sku)) {
                                    continue;
                                }
                                final DouDianSkuStockParam douDianSkuStockParam =
                                        new DouDianSkuStockParam();
                                final String skuId =
                                        testConfig
                                                .map(PlatformItemSyncConfig.ConfigItem::getSkuId)
                                                .orElse(sku.getOuterSkuId());
                                douDianSkuStockParam.setDouDianSkuId(Long.parseLong(skuId));
                                douDianSkuStockParam.setSkuType(0);
                                douDianSkuStockParam.setStockNum((long) sku.getSyncStock());
                                douDianSkuStockParam.setStepStockNum(0L);
                                paramList.add(douDianSkuStockParam);
                            }

                            final ShopAuthorization shopAuthorization =
                                    shopAuthorizationService
                                            .getByShopNo(platformItem.getShopNo())
                                            .filter(ShopAuthorization::isNotExpired)
                                            .orElseThrow(
                                                    () ->
                                                            ExceptionPlusFactory.bizException(
                                                                    ErrorCode.OPERATION_REJECT,
                                                                    "当前抖音店铺未授权或授权已过期"));
                            final SkuSyncStockBatchResponse skuSyncStockBatchResponse =
                                    douDianTemplate.syncStockBatch(
                                            shopAuthorization.getAccessToken(),
                                            Long.parseLong(outerItemId),
                                            paramList);
                            if (!skuSyncStockBatchResponse.isSuccess()) {
                                throw ExceptionPlusFactory.bizException(
                                        ErrorCode.PLATFORM_ITEM_STOCK_SYNC_ERROR,
                                        "抖店接口返回异常:" + skuSyncStockBatchResponse.getMsg());
                            }
                            return Response.buildSuccess();
                        },
                        skuFilter);
                break;
            case KUAISHOU:
                for (PlatformItemDetailSku sku : platformItemDetailSkuList) {
                    if (!skuFilter.test(sku)) {
                        continue;
                    }
                    updateStockThenRecord(
                            platformItem,
                            sku,
                            () -> {
                                final int syncStockDiff = sku.getSyncStock() - sku.getStock();
                                final String skuId =
                                        testConfig
                                                .map(PlatformItemSyncConfig.ConfigItem::getSkuId)
                                                .orElse(sku.getOuterSkuId());
                                kuaiShouServiceFactory
                                        .getService(platformItem.getShopNo())
                                        .skuStockUpdate(
                                                Long.parseLong(outerItemId),
                                                Long.parseLong(skuId),
                                                syncStockDiff > 0
                                                        ? StockChangeTypeEnum.INCREASE
                                                        : StockChangeTypeEnum.DECREASE,
                                                Math.abs(syncStockDiff));
                                return Response.buildSuccess();
                            });
                }
                break;
            case XIAOHONGSHU:
                for (PlatformItemDetailSku sku : platformItemDetailSkuList) {
                    if (!skuFilter.test(sku)) {
                        continue;
                    }
                    updateStockThenRecord(
                            platformItem,
                            sku,
                            () -> {
                                final String skuId =
                                        testConfig
                                                .map(PlatformItemSyncConfig.ConfigItem::getSkuId)
                                                .orElse(sku.getOuterSkuId());
                                redBookServiceFactory
                                        .getService(platformItem.getShopNo())
                                        .syncSkuStock(skuId, sku.getSyncStock());
                                return Response.buildSuccess();
                            });
                }
                break;
            case LAOBASHOP:
                for (PlatformItemDetailSku sku : platformItemDetailSkuList) {
                    if (!skuFilter.test(sku)) {
                        continue;
                    }
                    updateStockThenRecord(
                            platformItem,
                            sku,
                            () -> {
                                final String skuId =
                                        testConfig
                                                .map(PlatformItemSyncConfig.ConfigItem::getSkuId)
                                                .orElse(sku.getOuterSkuId());
                                final ErpItemSkuUpdateStockParam erpItemSkuUpdateStockParam =
                                        new ErpItemSkuUpdateStockParam();
                                erpItemSkuUpdateStockParam.setSkuId(Long.parseLong(skuId));
                                erpItemSkuUpdateStockParam.setType(StockTypeEnum.EDIT);
                                erpItemSkuUpdateStockParam.setNum(sku.getSyncStock());

                                final SingleResponse<Boolean> response =
                                        arkSailorItemBizService.updateStock(
                                                erpItemSkuUpdateStockParam);
                                ResponseAssert.assertJust(response);
                                return Response.buildSuccess();
                            });
                }
                break;
            case PDD:
                for (PlatformItemDetailSku sku : platformItemDetailSkuList) {
                    if (!skuFilter.test(sku)) {
                        continue;
                    }
                    updateStockThenRecord(
                            platformItem,
                            sku,
                            () -> {
                                final String skuId =
                                        testConfig
                                                .map(PlatformItemSyncConfig.ConfigItem::getSkuId)
                                                .orElse(sku.getOuterSkuId());
                                pddStockService.goodsQuantityUpdate(
                                        platformItem.getShopNo(),
                                        Long.parseLong(outerItemId),
                                        Long.parseLong(skuId),
                                        Long.valueOf(sku.getSyncStock()),
                                        1);
                                return Response.buildSuccess();
                            });
                }
            default:
                throw new UnsupportedOperationException(platform.getDesc() + "暂不支持库存同步");
        }

        return Response.buildSuccess();
    }

    private boolean getSkuFilter(PlatformItemDetailSku sku) {
        return sku.getStatus() == PlatformItemStatus.ON_SALE;
    }

    private void updateStockThenRecord(
            PlatformItem platformItem,
            PlatformItemDetailSku detailSku,
            Supplier<Response> handler) {
        final StockSyncRecord stockSyncRecord = new StockSyncRecord();
        stockSyncRecord.setPlatform(platformItem.getPlatform());
        stockSyncRecord.setSyncDirection(StockSyncDirection.UPLOAD);
        stockSyncRecord.setSyncStatus(StockSyncStatus.SUCCESS);
        stockSyncRecord.setOuterItemId(platformItem.getOuterItemId());
        stockSyncRecord.setOuterSkuId(detailSku.getOuterSkuId());
        stockSyncRecord.setItemCode(platformItem.getOuterItemCode());
        stockSyncRecord.setSkuCode(detailSku.getOuterSkuCode());
        stockSyncRecord.setBeforeStock(detailSku.getStock());
        stockSyncRecord.setStock(detailSku.getSyncStock());
        try {
            final Response response = handler.get();
            ResponseAssert.assertJust(response);
        } catch (Exception e) {
            stockSyncRecord.setSyncStatus(StockSyncStatus.FAIL);
            stockSyncRecord.setMsg(e.getMessage());
        }
        stockSyncRecordService.save(stockSyncRecord);
    }

    private void updateStockThenRecord(
            PlatformItem platformItem,
            List<PlatformItemDetailSku> skus,
            Supplier<Response> handler,
            Predicate<PlatformItemDetailSku> skuFilter) {
        StockSyncStatus syncStatus = StockSyncStatus.SUCCESS;
        String msg = null;
        try {
            final Response response = handler.get();
            ResponseAssert.assertJust(response);
        } catch (Exception e) {
            syncStatus = StockSyncStatus.FAIL;
            msg = e.getMessage();
        }
        final ArrayList<StockSyncRecord> stockSyncRecords = new ArrayList<>();
        for (PlatformItemDetailSku detailSku : skus) {
            if (!skuFilter.test(detailSku)) {
                continue;
            }
            final StockSyncRecord stockSyncRecord = new StockSyncRecord();
            stockSyncRecord.setPlatform(platformItem.getPlatform());
            stockSyncRecord.setSyncDirection(StockSyncDirection.UPLOAD);
            stockSyncRecord.setSyncStatus(syncStatus);
            stockSyncRecord.setMsg(msg);
            stockSyncRecord.setOuterItemId(platformItem.getOuterItemId());
            stockSyncRecord.setOuterSkuId(detailSku.getOuterSkuId());
            stockSyncRecord.setItemCode(platformItem.getOuterItemCode());
            stockSyncRecord.setSkuCode(detailSku.getOuterSkuCode());
            stockSyncRecord.setBeforeStock(detailSku.getStock());
            stockSyncRecord.setStock(detailSku.getSyncStock());
            stockSyncRecords.add(stockSyncRecord);
        }
        stockSyncRecordService.saveBatch(stockSyncRecords);
    }

    @Override
    public PageResponse<SyncLogVO> syncLog(SyncLogQuery query) {
        final LocalDateTime syncStartTime =
                Optional.ofNullable(query.getSyncStartTime())
                        .filter(NumberUtil::isPositive)
                        .map(time -> LocalDateTimeUtil.of(time * 1000))
                        .orElseGet(
                                () ->
                                        LocalDateTime.now()
                                                .minusDays(2)
                                                .toLocalDate()
                                                .atStartOfDay());
        final LocalDateTime syncEndTime =
                Optional.ofNullable(query.getSyncEndTime())
                        .filter(NumberUtil::isPositive)
                        .map(time -> LocalDateTimeUtil.of(time * 1000))
                        .orElseGet(LocalDateTimeUtil::now);
        final Duration duration = Duration.between(syncStartTime, syncEndTime);
        if (duration.toDays() > 3) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "选择时间范围不能大于3天");
        }
        final IPage<StockSyncRecord> page =
                stockSyncRecordService
                        .lambdaQuery()
                        .eq(
                                query.getPlatform() != null,
                                StockSyncRecord::getPlatform,
                                query.getPlatform())
                        .ge(
                                StockSyncRecord::getCreatedAt,
                                LocalDateTimeUtil.toEpochMilli(syncStartTime) / 1000)
                        .le(
                                StockSyncRecord::getCreatedAt,
                                LocalDateTimeUtil.toEpochMilli(syncEndTime) / 1000)
                        .eq(
                                StringUtil.isNotBlank(query.getOuterItemId()),
                                StockSyncRecord::getOuterItemId,
                                query.getOuterItemId())
                        .eq(
                                StringUtil.isNotBlank(query.getSkuCode()),
                                StockSyncRecord::getSkuCode,
                                query.getSkuCode())
                        .orderByDesc(StockSyncRecord::getCreatedAt)
                        .page(query.getPage());
        return ResponseFactory.ofPage(page, Assembler.MAPPER::stockSyncRecordPoToSyncLogVo);
    }

    @Override
    public void putSyncLogAsync(StockSyncRecord record) {
        // 允许丢器
        queue.offer(record);
    }

    @Override
    public Response explicitlySync(SwitchExplicitlySyncCmd cmd) {
        final PlatformItem platformItem = new PlatformItem();
        platformItem.setId(cmd.getPlatformItemId());
        platformItem.setSyncEnabled(cmd.isExplicitlySync());
        platformItemService.updateById(platformItem);
        return Response.buildSuccess();
    }

    @Mapper(
            uses = {StaffAssembler.class, CommonEnumTransMapper.class},
            imports = {IEnum.class, Platform.class})
    public interface Assembler {
        Assembler MAPPER = Mappers.getMapper(Assembler.class);

        @Mapping(target = "syncTime", source = "createdAt")
        @Mapping(
                target = "syncStatus",
                expression = "java(stockSyncRecordToStatusMsg(stockSyncRecord))")
        @Mapping(target = "staffBrief", source = "createdUid")
        @Mapping(target = "skuCode", source = "skuCode")
        @Mapping(target = "platform", source = "platform")
        @Mapping(target = "outerItemId", source = "outerItemId")
        @Mapping(target = "msg", source = "msg")
        SyncLogVO stockSyncRecordPoToSyncLogVo(StockSyncRecord stockSyncRecord);

        default String stockSyncRecordToStatusMsg(StockSyncRecord record) {
            if (record == null) {
                return "";
            }
            final String msg =
                    Optional.ofNullable(record.getMsg())
                            .filter(StringUtil::isNotBlank)
                            .map(s -> s.replaceAll("\\[[^\\[\\]]+\\]", ""))
                            .orElse("");
            return String.format(
                    "库存%s%s！%s",
                    record.getSyncDirection().getDesc(), record.getSyncStatus().getDesc(), msg);
        }
    }
}

package com.daddylab.supplier.item.types.itemOptimize;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import org.javers.core.metamodel.annotation.DiffIgnore;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@ApiModel("商品优化数据模型")
@Data
public class ItemOptimizePersistData {
    /** 商品链接 */
    @ApiModelProperty(value = "商品链接")
    private String link;

    /** 链接产品标题 */
    @ApiModelProperty(value = "链接产品标题")
    private String linkTitle;

    /** P系统技术类目 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货 */
    @ApiModelProperty(value = "P系统技术类目 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货")
    private Integer psysType;

    /** 平台建议标题【可补充】热搜词 */
    @ApiModelProperty(value = "平台建议标题【可补充】热搜词")
    private String canBeSupplementedHotSearchKeywords;

    /** 采购负责人 */
    @ApiModelProperty(value = "采购负责人")
    private List<StaffBrief> buyerUsers;

    /** QC负责人 */
    @ApiModelProperty(value = "QC负责人")
    private List<StaffBrief> qcUsers;

    /** 法务负责人 */
    @ApiModelProperty(value = "法务负责人")
    private List<StaffBrief> legalUsers;

    /** 修改负责人 */
    @ApiModelProperty(value = "修改负责人")
    private List<StaffBrief> modifyUsers;

    /** 修改时间 */
    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;

    /** 是否已完成修改 */
    @ApiModelProperty(value = "链接产品是否修改")
    private Boolean modified;

    /** 触发重新审核标识 key -> 流程ID */
    @ApiModelProperty(value = "触发重新审核标识", hidden = true)
    @DiffIgnore
    private Map<String, Boolean> flagsOfTriggerReAudit = Collections.emptyMap();

    /**
     * 禁止撤回
     */
    @ApiModelProperty(value = "禁止撤回", hidden = true)
    private Boolean disableRollback = false;
}

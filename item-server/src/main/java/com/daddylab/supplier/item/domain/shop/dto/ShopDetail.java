package com.daddylab.supplier.item.domain.shop.dto;


import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.vo.ShopPrincipalVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 店铺
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode()
public class ShopDetail implements Serializable {
    /**
     * id
     */
    @PropertyName("ID")
    @Id
    protected Long id;

    /**
     * 店铺编号
     */
    @PropertyName("店铺编号")
    private String sn;

    /**
     * 店铺名称
     */
    @PropertyName("店铺名称")
    private String name;

    /**
     * 店铺账号
     */
    @PropertyName("店铺账号")
    private String account;

    /**
     * 店铺链接
     */
    @PropertyName("店铺链接")
    private String link;

    /**
     * 店铺LOGO
     */
    @PropertyName("店铺LOGO")
    private String logo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    @PropertyName("平台")
    private Platform platform;

    /**
     * 状态 0:关闭 1:准备中 2:营业
     */
    @PropertyName("状态")
    private ShopStatus status;

    /**
     * 店铺负责人
     */
    @PropertyName("店铺负责人")
    private List<ShopPrincipalVo> principals;

    /**
     * 合作模式
     */
    @PropertyName("合作模式")
    private List<Integer> businessLine;


    @NotNull
    @ApiModelProperty("是否开启自动分配库存.0否,1是")
    private Integer autoAllocateInventory;

    @ApiModelProperty("设置自动同步库存频次.单位秒")
    private Integer syncFrequency;

    @ApiModelProperty("设置安全库存")
    private Integer safetyThreshold;

    @ApiModelProperty("下属店铺仓库配置明细")
    private List<ShopWarehouseInventoryVo> shopWarehouseInventoryVos;

    /**
     * 系统设置。
     */
    @ApiModelProperty("支持店铺自行设置库存同步频次")
    private Integer permitShopSyncFreqSetting;

    @ApiModelProperty("库存模式")
    private InventoryMode inventoryMode = InventoryMode.SHARED;

    @PropertyName("店铺主体公司")
    private String mainCompany;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("运营模式")
    private List<Integer> runningMode;

}

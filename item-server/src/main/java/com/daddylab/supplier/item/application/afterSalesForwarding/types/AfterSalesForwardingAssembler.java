package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.trans.TimeTransMapper;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleForwardingRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSaleForwardingRegisterStatusEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Mapper(imports = {
        TimeTransMapper.class, CommonAssembler.class, IEnum.class
}, uses = {})
public interface AfterSalesForwardingAssembler {
    AfterSalesForwardingAssembler INSTANCE = Mappers.getMapper(AfterSalesForwardingAssembler.class);

    @Mapping(target = "affectsSalesVouchers", ignore = true)
    @Mapping(target = "intact", expression = "java(CommonAssembler.INST.stringToBoolean(value.getIntact()))")
    @Mapping(target = "itemNum", source = "itemNum")
    @Mapping(target = "registerTime", expression = "java(TimeTransMapper.INSTANCE.formattedStringToTimestamp(value.getRegisterTime()))")
    AfterSalesForwardingRegisterFormItemVO excelToFormItemObj(AfterSalesForwardingRegisterImportExcelVO value);

    @Mapping(target = "affectsSalesVouchers", expression = "java(mapAffectsSalesVouchersToStr(value.getAffectsSalesVouchers()))")
    @Mapping(target = "registerTime", expression = "java(TimeTransMapper.INSTANCE.timestampToFormattedString(value.getRegisterTime()))")
    @Mapping(target = "status", expression = "java(mapAfterSaleForwardingRegisterStatusEnumValueToDescStr(value.getStatus()))")
    @Mapping(target = "intact", expression = "java(CommonAssembler.INST.booleanToString(value.getIntact()))")
    AfterSalesForwardingRegisterExcelVO pageToExcelObj(AfterSalesForwardingRegisterPageVO value);

    default String mapAfterSaleForwardingRegisterStatusEnumValueToDescStr(Integer value) {
        return IEnum.getEnumOptByValue(AfterSaleForwardingRegisterStatusEnum.class, value).map(AfterSaleForwardingRegisterStatusEnum::getDesc).orElse("");
    }

    default String mapAffectsSalesVouchersToStr(AffectsSalesVouchers value) {
        return value == null ? "" : Stream.of(value.getImages(), value.getVideos())
                                          .flatMap(Collection::stream)
                                          .map(AffectsSalesVouchers.Voucher::getUrl)
                                          .collect(Collectors.joining("\n"));
    }

    default String mapAffectsSalesVouchersToJsonStr(AffectsSalesVouchers value) {
        return value != null ? JsonUtil.toJson(value) : null;
    }

    default AffectsSalesVouchers mapAffectsSalesVoucherJsonStrToObj(String value) {
        return value != null ? JsonUtil.parse(value, AffectsSalesVouchers.class) : null;
    }

    @Mapping(target = "affectsSalesVouchers", expression = "java(mapAffectsSalesVouchersToJsonStr(value.getAffectsSalesVouchers()))")
    @Mapping(target = "createdAt", source = "registerTime")
    AfterSaleForwardingRegister afterSaleForwardingRegisterFormItemVoToPo(AfterSalesForwardingRegisterFormItemVO value);

    @Mapping(target = "affectsSalesVouchers", expression = "java(mapAffectsSalesVoucherJsonStrToObj(value.getAffectsSalesVouchers()))")
    @Mapping(target = "registerTime", source = "createdAt")
    AfterSalesForwardingRegisterFormItemVO afterSaleForwardingRegisterPoToFormItemVo(AfterSaleForwardingRegister value);

}

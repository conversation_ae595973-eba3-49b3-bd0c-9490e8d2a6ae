package com.daddylab.supplier.item.types.refundOrder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesSendOnInfoVO;
import com.daddylab.supplier.item.infrastructure.jackson.BigDecimalScale4Serializer;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
@Data
@ApiModel("退换单详情（P系统）")
public class RefundOrderDetailForP {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    Long id;

    /**
     * 退款单号
     **/
    @ApiModelProperty("退款单号")
    String refundOrderNo;

    /**
     * 原始订单号
     **/
    @ApiModelProperty("原始订单号")
    String srcOrderNos;

    /**
     * 关联订单号（旺店通）
     **/
    @ApiModelProperty("关联订单号（旺店通）")
    String wdtOrderNos;

    /**
     * 退换状态 10:已取消;20:待审核;30:已审核;40已推送:80:已结算;81:待回传;85:待过账;86:已过账;87:成本确认;90:已完成
     **/
    @ApiModelProperty("退换状态 10:已取消;20:待审核;30:已审核;40已推送:80:已结算;81:待回传;85:待过账;86:已过账;87:成本确认;90:已完成")
    Integer status;

    /**
     * 售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款
     **/
    @ApiModelProperty("售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款")
    Integer type;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    Long createTime;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    String creatorName;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    String remark;

    /**
     * 退回物流单号
     **/
    @ApiModelProperty("退回物流单号")
    String returnLogisticsNo;

    /**
     * 退回物流
     **/
    @ApiModelProperty("退回物流")
    String returnLogisticsName;

    /**
     * 退回仓库编号
     **/
    @ApiModelProperty("退回仓库编号")
    String returnWarehouseNo;

    /**
     * 退回仓库名称
     **/
    @ApiModelProperty("退回仓库名称")
    String returnWarehouseName;

    /**
     * 退回商品明细
     */
    @ApiModelProperty("退回商品明细")
    List<RefundOrderItem> returnItemDetails;

    /**
     * 换货商品明细
     */
    @ApiModelProperty("换货商品明细")
    List<RefundOrderItem> swapItemDetails;

    /**
     * 申请退货金额
     **/
    @ApiModelProperty("申请退货金额")
    @JsonGetter
    @JsonSerialize(using = BigDecimalScale4Serializer.class)
    public BigDecimal getActualRefundAmount() {
        return getTotalAmount();
    }

    /**
     * 实际退货金额
     **/
    @ApiModelProperty("实际退货金额")
    @JsonGetter
    @JsonSerialize(using = BigDecimalScale4Serializer.class)
    public BigDecimal getApplyRefundAmount() {
        return getTotalRefundAmount();
    }

    /**
     * 原始退换单号
     **/
    @ApiModelProperty("原始退换单号")
    public List<String> getSrcRefundOrderNo() {
        if (CollUtil.isNotEmpty(returnItemDetails)) {
            return returnItemDetails.stream().map(RefundOrderItem::getRawRefundNos)
                    .filter(Objects::nonNull)
                    .flatMap(nos -> StrUtil.splitTrim(nos, ',').stream())
                    .distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * @return 退款/退货总数量
     */
    @JsonGetter
    @ApiModelProperty("退款/退货总数量")
    public Integer getTotalRefundQuantity() {
        if (CollUtil.isNotEmpty(returnItemDetails)) {
            return returnItemDetails.stream().map(RefundOrderItem::getRefundQuantity).filter(Objects::nonNull).reduce(
                    Integer::sum).orElse(0);
        }
        return 0;
    }

    /**
     * @return 退款/退货总金额
     */
    @JsonGetter
    @ApiModelProperty("退款/退货总金额")
    @JsonSerialize(using = BigDecimalScale4Serializer.class)
    public BigDecimal getTotalRefundAmount() {
        if (CollUtil.isNotEmpty(returnItemDetails)) {
            return returnItemDetails.stream().map(RefundOrderItem::getRefundAmount).filter(Objects::nonNull).reduce(
                    BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    /**
     * @return 商品总金额
     */
    @JsonGetter
    @ApiModelProperty("商品总金额")
    @JsonSerialize(using = BigDecimalScale4Serializer.class)
    public BigDecimal getTotalAmount() {
        if (CollUtil.isNotEmpty(returnItemDetails)) {
            return returnItemDetails.stream().map(RefundOrderItem::getTotalAmount).filter(Objects::nonNull).reduce(
                    BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    /**
     * @return 换货总数量
     */
    @JsonGetter
    @ApiModelProperty("换货总数量")
    public Integer getTotalSwapQuantity() {
        if (CollUtil.isNotEmpty(swapItemDetails)) {
            return swapItemDetails.stream().map(RefundOrderItem::getOrderQuantity)
                    .filter(Objects::nonNull)
                    .reduce(Integer::sum).orElse(0);
        }
        return 0;
    }

    /**
     * 退货地址
     */
    @ApiModelProperty("退货地址")
    private String returnAddress;

    /**
     * 退货联系人
     */
    @ApiModelProperty("退货联系人")
    private String returnContacts;

    /**
     * 退货联系电话
     */
    @ApiModelProperty("退货联系电话")
    private String returnTel;

    /**
     * 转寄信息
     */
    @ApiModelProperty("转寄信息")
    List<AfterSalesSendOnInfoVO> sendOnInfos;

    /**
     * 登记收货信息
     */
    @ApiModelProperty("登记收货信息")
    AfterSalesReceiveInfoVO receiveInfo;

    /**
     * 销退确认信息
     */
    @ApiModelProperty("销退确认信息")
    AfterSalesConfirmInfoVO confirmInfo;

}

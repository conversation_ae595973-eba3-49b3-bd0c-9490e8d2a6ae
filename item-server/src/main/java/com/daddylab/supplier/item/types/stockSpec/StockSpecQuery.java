package com.daddylab.supplier.item.types.stockSpec;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("库存列表查选参数封装")
public class StockSpecQuery extends PageQuery {
    private static final long serialVersionUID = -1419042545431809310L;

    @ApiModelProperty("库存规格ID")
    private List<Long> ids;

    @ApiModelProperty("实仓仓库ID")
    private List<Long> warehouseIds;

    @ApiModelProperty("实仓仓库编码")
    private List<String> warehouseNos;

    @ApiModelProperty("实仓仓库名称")
    private String warehouseName;

    @ApiModelProperty("SKU 编码")
    private List<String> skuCodes;

    @ApiModelProperty("SPU 编码")
    private List<String> spuCodes;

    @ApiModelProperty("品牌 ID")
    private List<Long> brandIds;

    @ApiModelProperty("品牌编码")
    private List<String> brandNos;

    @ApiModelProperty("商品名称")
    private List<String> itemNames;

    @ApiModelProperty("品类 ID")
    private List<Long> categoryIds;

    @ApiModelProperty("合作方（新）")
    private List<Integer> corpType = new LinkedList<>();

    @ApiModelProperty("合作模式（旧）")
    private List<Integer> businessLine = new LinkedList<>();

    @ApiModelProperty("业务类型")
    private List<Integer> bizType = new LinkedList<>();
}


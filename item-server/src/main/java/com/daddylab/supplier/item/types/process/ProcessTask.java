package com.daddylab.supplier.item.types.process;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@Data
public class ProcessTask {
    /** 任务ID */
    @ApiModelProperty("任务ID")
    String id;

    /** 任务名称 */
    @ApiModelProperty("任务名称")
    String name;

    /** 任务当前处理人 */
    @ApiModelProperty("任务当前处理人")
    StaffBrief assignee;

    /** 候选人 */
    @ApiModelProperty("候选人")
    List<StaffBrief> candidates;

    /** 是否可认领 */
    @ApiModelProperty("是否可认领")
    private Boolean claimable;

    public Boolean getClaimable() {
        if (claimable != null) {
            return claimable;
        }
        final Long userId = UserContext.getUserId();
        final boolean hasAssignee = assignee != null;
        final boolean isCandidate = isCandidate(userId);
        return !hasAssignee && isCandidate;
    }

    private boolean isCandidate(Long userId) {
        return candidates != null
                && candidates.stream()
                        .anyMatch(candidate -> Objects.equals(userId, candidate.getUserId()));
    }

    /** 当前用户是否可提交此任务 */
    @ApiModelProperty("当前用户是否可提交此任务")
    private Boolean completable;

    public Boolean getCompletable() {
        if (completable != null) {
            return completable;
        }
        final Long userId = UserContext.getUserId();
        return (completeTime == null || completeTime == 0)
                && (isCandidate(userId)
                || assignee != null
                && Objects.equals(assignee.getUserId(), userId));
    }

    /** 认领时间 */
    @ApiModelProperty("认领时间")
    private Long claimTime;

    /** 备注 */
    @ApiModelProperty("备注")
    List<Comment> comments;

    /** 任务创建时间 */
    @ApiModelProperty("任务创建时间")
    private Long createTime;

    /** 完成时间 */
    @ApiModelProperty("完成时间")
    private Long completeTime;

    @ApiModelProperty("是否已完成")
    public boolean isCompleted() {
        return completeTime != null && completeTime != 0;
    }

    @ApiModelProperty("是否为进行中的任务")
    public boolean isActive() {
        return completeTime == null || completeTime == 0;
    }
}

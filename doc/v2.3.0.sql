ALTER TABLE `purchase`
    ADD `price_type` int(2) DEFAULT 0 NULL COMMENT '0:采购活动价。1:sku日常阶梯价。2:sku活动阶梯价。3:spu日常阶梯价。4:spu活动阶梯价'
;

alter table item_drawer
    add dou_title varchar(64) default '' not null comment '抖音标题';

create table item_optimize_plan
(
    id            bigint auto_increment
        primary key comment 'id',
    platform      tinyint      default 0   not null comment '平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城',
    plan_no       varchar(15)  default ''  not null comment '编号',
    plan_name     varchar(100) default ''  not null comment '名称',
    item_num      int          default 0   not null comment '商品数量',
    status        tinyint      default 0   not null comment '0:未提交，1已提交',
    business_line varchar(32)  default '0' null comment '合作模式（业务线）：0电商 1老爸抽检 2绿色家装 3商家入驻（目前为单选）',
    submit_at     bigint       default 0   not null comment '提交时间',
    submit_uid    bigint       default 0   not null comment '提交人',
    created_at    bigint       default 0   not null comment '创建时间',
    created_uid   bigint       default 0   not null comment '创建人',
    updated_at    bigint       default 0   not null comment '更新时间',
    updated_uid   bigint       default 0   not null comment '更新人',
    is_del        tinyint      default 0   not null comment '是否删除：0否1是',
    deleted_at    bigint       default 0   not null comment '删除时间',
    unique `plan_no__uindex` (plan_no, deleted_at)
)
    comment '商品优化计划';

create table item_optimize
(
    id               bigint auto_increment
        primary key comment 'id',
    platform         tinyint     default 0  not null comment '平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城',
    plan_id          bigint                 not null comment '商品优化计划ID',
    outer_item_id    varchar(40) default '' not null comment '对应销售平台的货品ID',
    platform_item_id bigint      default 0  not null comment '平台货品ID（系统内部的平台货品），未匹配为0',
    item_id          bigint      default 0  not null comment '商品ID，未匹配为0',
    status           int         default 0  not null comment '状态 0 待提交 1 已提交 2 待QC审核 3 待法务审核 4 待修改 5 已完成',
    plan_status      tinyint     default 0  not null comment '优化计划状态 0:未提交，1已提交',
    submit_at        bigint      default 0  not null comment '提交时间',
    submit_uid       bigint      default 0  not null comment '提交人',
    process_inst_id  varchar(64) default '' not null comment '当前流程实例ID',
    data             json comment '商品优化模型数据',
    v                int         default 0 comment '版本号（做乐观锁用）',
    created_at       bigint      default 0  not null comment '创建时间',
    created_uid      bigint      default 0  not null comment '创建人',
    updated_at       bigint      default 0  not null comment '更新时间',
    updated_uid      bigint      default 0  not null comment '更新人',
    is_del           tinyint     default 0  not null comment '是否删除：0否1是',
    deleted_at       bigint      default 0  not null comment '删除时间',
    `link_title`     varchar(200) AS (JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.linkTitle'))) STORED comment '链接标题',
    `psys_type`      tinyint AS (JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.psysType'))) comment 'P系统类目',
    key `plan_id__index` (plan_id),
    key `psys_type__index` (psys_type)
)
    comment '商品优化';

create table item_optimize_participant
(
    id               bigint auto_increment
        primary key comment 'id',
    item_optimize_id bigint            not null comment '商品优化ID',
    type             tinyint           not null comment '参与者类型 1:采购负责人 2:法务负责人; 3:QC负责人; 4:修改负责人;',
    uid              bigint            not null comment '用户ID',
    created_at       bigint  default 0 not null comment '创建时间',
    created_uid      bigint  default 0 not null comment '创建人',
    updated_at       bigint  default 0 not null comment '更新时间',
    updated_uid      bigint  default 0 not null comment '更新人',
    is_del           tinyint default 0 not null comment '是否删除：0否1是',
    deleted_at       bigint  default 0 not null comment '删除时间',
    unique key uid_item_optimize_id_type__uindex (uid, item_optimize_id, type, deleted_at),
    key item_optimize_id__index (item_optimize_id, type, uid)
)
    comment '商品优化参与者';

create table process_inst_ref
(
    id              bigint auto_increment
        primary key comment 'id',
    process_inst_id varchar(50)       not null comment '审核流程实例ID',
    type            int               not null comment '业务类型 1 商品优化 2...',
    business_id     bigint            not null comment '业务ID',
    created_at      bigint  default 0 not null comment '创建时间',
    created_uid     bigint  default 0 not null comment '创建人',
    updated_at      bigint  default 0 not null comment '更新时间',
    updated_uid     bigint  default 0 not null comment '更新人',
    is_del          tinyint default 0 not null comment '是否删除：0否1是',
    deleted_at      bigint  default 0 not null comment '删除时间',
    key `type_business_id__index` (type, business_id, process_inst_id),
    key `process_inst_id__index` (process_inst_id)
) comment '流程业务关联';

create table item_optimize_advice
(
    id               bigint auto_increment comment 'id'
        primary key comment 'id',
    item_optimize_id bigint                 not null comment '后端优化ID',
    process_inst_id  varchar(64)            not null comment '流程实例ID',
    node_id          varchar(64)            not null comment '流程节点ID',
    node_key         varchar(64)            not null comment '流程节点关键字',
    module           varchar(32) default '' not null comment '模块',
    advice_target    varchar(32) default '' not null comment '该条建议是针对哪一部分内容的建议',
    advice_content   text                   null comment '建议内容',
    created_at       bigint      default 0  not null comment '创建时间createAt',
    created_uid      bigint      default 0  not null comment '创建人updateUser',
    updated_at       bigint      default 0  not null comment '更新时间updateAt',
    updated_uid      bigint      default 0  not null comment '更新人updateUser',
    is_del           tinyint     default 0  not null comment '是否已删除',
    deleted_at       bigint      default 0  not null comment '删除时间'
)
    comment '商品优化建议';


create index idx_logistics_name
    on wdt_sale_stock_out_order (`logistics_name`);
create index idx_logistics_no
    on wdt_sale_stock_out_order (`logistics_no`);


ALTER TABLE `platform_item_sku`
    ADD `item_id`  bigint  DEFAULT 0 NOT NULL COMMENT '匹配到的我们自己的商品ID',
    ADD `platform` tinyint DEFAULT 0 NOT NULL COMMENT '平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城'
;

UPDATE `platform_item_sku` `pis` JOIN `platform_item` `pi` ON `pis`.`platform_item_id` = `pi`.`id`
SET `pis`.`item_id`  = `pi`.`item_id`,
    `pis`.`platform` = `pi`.`platform`
WHERE `pis`.`item_id` = 0
;
alter table item_sku
    add purchase_tax_rate decimal(10, 6) null comment '采购税率';

alter table item_procurement modify rate decimal (11,6) default 0.000000 not null comment '商品税率';

alter table item_procurement
    add purchase_rate decimal(11, 6) default 0.000000 null comment '采购税率';




create table purchase_payable_order_relation
(
    id                    bigint auto_increment comment 'id'
        primary key,
    created_at            bigint null comment '创建时间',
    updated_at            bigint null comment '更新时间',
    created_uid           bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid           bigint null comment '更新者',
    deleted_at            bigint null comment '删除时间',
    is_del                tinyint(2) default 0 null comment '是否删除',
    resource_no           varchar(30) not null comment '采购应付单原编码',
    relate_stock_order_no varchar(50) not null comment '冲销过程中生成的出库单编码',
    type                  int null comment '关联类型。1：完全冲销。2：修正数据后生成'
) comment '采购应付关联信息表';



create table purchase_payable_apply_order
(
    id                      bigint auto_increment comment 'id'
        primary key,
    created_at              bigint null comment '创建时间',
    updated_at              bigint null comment '更新时间',
    created_uid             bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid             bigint null comment '更新者',
    deleted_at              bigint null comment '删除时间',
    is_del                  tinyint(2) default 0 null comment '是否删除',
    no                      varchar(30)    not null comment '编码',
    apply_date              bigint(30) not null comment '申请日期。',
    provider_id             bigint         not null comment '供应商id',
    provider_name           varchar(50)    not null comment '供应商名称',
    remark                  text null comment '备注',
    status                  int(2) default 1 not null comment '状态。1。保存（待审核)',
    source_pay_total_amount decimal(20, 6) not null comment '原本累计金额',
    fixed_total_amount      decimal(20, 6) not null comment '修正后的金额'
) comment '采购付款申请单';



create table purchase_payable_apply_order_detail
(
    id                    bigint auto_increment comment 'id'
        primary key,
    created_at            bigint null comment '创建时间',
    updated_at            bigint null comment '更新时间',
    created_uid           bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid           bigint null comment '更新者',
    deleted_at            bigint null comment '删除时间',
    is_del                tinyint(2) default 0 null comment '是否删除',
    apply_order_no        varchar(30)    not null comment '付款请单编号',
    apply_order_id        bigint(30) not null comment '付款申请单id',
    purchase_pay_order_no varchar(50)    not null comment '应付单编号',
    relation_order_id     varchar(50) null comment '原应付单的关联单号id',
    relation_order_type   int null comment '1:采购入库。2:退料出库',
    sku_code              varchar(50)    not null comment 'sku编号',
    fixed_quantity        int(10) not null comment '修正数量',
    fixed_total_amount    decimal(20, 6) not null comment '修正金额',
    specifications        varchar(256) null comment '规格详情',
    item_name             varchar(100) null comment '商品名称',
    quantity              int(10) null comment '数量',
    tax_rate              decimal(20, 6) null comment '税率',
    with_tax_price        decimal(20, 6) null comment '含税价格，税前单价',
    without_tax_price     decimal(20, 6) null comment '不含税价格，税后单价',
    tax_total_amount      decimal(20, 6) null comment '总税额',
    with_tax_total_amount decimal(20, 6) null comment '价税合计',
    warehouse_no          varchar(100) null comment '仓库编号',
    provider_id           bigint null comment '供应商id',
    error_log             text null comment '错误日志'
) comment '采购付款申请单详情';



create table additional
(
    id           bigint auto_increment comment 'id'
        primary key,
    created_at   bigint null comment '创建时间',
    updated_at   bigint null comment '更新时间',
    created_uid  bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid  bigint null comment '更新者',
    deleted_at   bigint null comment '删除时间',
    is_del       tinyint(2) default 0 null comment '是否删除',
    name         varchar(256) null comment '附件名称',
    download_url varchar(512) null comment '附件下载地址'

) comment '附件列表';


create table pay_apply_audit_process
(
    id             bigint auto_increment comment 'id'
        primary key,
    created_at     bigint null comment '创建时间',
    updated_at     bigint null comment '更新时间',
    created_uid    bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid    bigint null comment '更新者',
    deleted_at     bigint null comment '删除时间',
    is_del         tinyint(2) default 0 null comment '是否删除',
    apply_order_id bigint(30) null comment '申请付款编号',
    seq            int(2) null comment '审核序列',
    node_id        bigint(30) null comment '节点角色审核信息详情id',
    node_status    int(2) default 0 not null comment '此节点审核状态。1:过审。-1:拒绝。0:未处理',
    auto_audit     int(2) default 0 null comment '是否自动审核'
) comment '申请付款单审核流程表';



create table pay_apply_audit_process_node
(
    id           bigint auto_increment comment 'id'
        primary key,
    created_at   bigint null comment '创建时间',
    updated_at   bigint null comment '更新时间',
    created_uid  bigint null comment '创建者（关联入库单/出库单/其他应付的创建人）',
    updated_uid  bigint null comment '更新者',
    deleted_at   bigint null comment '删除时间',
    is_del       tinyint(2) default 0 null comment '是否删除',
    node_id      bigint(30) null comment '审核节点id',
    role_name    varchar(30) not null comment '审核角色名称',
    audit_status int(2) default 0 not null comment '审核状态。1：过审。-1：拒绝。0：初始化',
    user_id      bigint(30) null comment '用户id',
    user_name    varchar(30) null comment '用户名称',
    opinion      varchar(512) null comment '审核意见',
    auto_audit   int(2) default 0 null comment '是否自动审核'
) comment '申请付款单审核流节点审核详情';


ALTER TABLE `purchase_payable_order_relation`
    ADD KEY `idx_resource_no` (`resource_no`);

ALTER TABLE `purchase_payable_apply_order`
    ADD KEY `idx_no` (`no`);

ALTER TABLE `purchase_payable_apply_order_detail`
    ADD KEY `idx_apply_id` (`apply_order_id`),
    ADD KEY `idx_apply_no` (`apply_order_no`),
    ADD KEY `idx_source_no` (`purchase_pay_order_no`);

ALTER TABLE `pay_apply_audit_process`
    ADD KEY `idx_apply_order_id` (`apply_order_id`),
    ADD KEY `idx_node_id` (`node_id`);

ALTER TABLE `pay_apply_audit_process_node`
    ADD KEY `idx_node_id` (`node_id`);

-- 2022-04-21 新增
alter table purchase_payable_apply_order
    add additional_id bigint(13) default null comment '附件ID';
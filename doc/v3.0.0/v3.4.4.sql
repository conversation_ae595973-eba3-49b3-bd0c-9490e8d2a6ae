alter table `shop`
    add column `main_company` varchar(128) default '' comment '店铺主体公司';


INSERT INTO `msg_template` (`created_at`, `status`, `code`, `name`, `title`, `content`, `receiver`, `link`, `remark`)
VALUES (unix_timestamp(), 1, 'HANDING_SHEET_CONFIRM', '盘货表变动(采购确认)',
        '盘货表确认：${采购员}确认了${盘货表名称}信息，请及时查看！', '已确认${商品数量}个商品信息', '${活动成员}',
        '${sys.domain}/operation-management/inventory/edit?id=${sheetId}&state=-1', '盘货表变动'),
       (unix_timestamp(), 1, 'HANDING_SHEET_UPDATE', '盘货表变动(采购修改)',
        '盘货表修改：${采购员}修改了${盘货表名称}信息，请及时查看！', '已修改${商品数量}个商品信息', '${活动成员}',
        '${sys.domain}/operation-management/inventory/edit?id=${sheetId}&state=-1', '盘货表变动');



ALTER TABLE `after_sale_forwarding_register`
    ADD FULLTEXT `forwarding_address__findex` (`forwarding_address`) WITH PARSER `ngram`
;
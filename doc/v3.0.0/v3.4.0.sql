alter table warehouse_after_sales_address
    add column `remark`        varchar(500) default '' comment '备注',
    add column `province_code` varchar(50)  default '' comment '省编码',
    add column `city_code`     varchar(50)  default '' comment '城市编码',
    add column `area_code`     varchar(50)  default '' comment '区县编码';
alter table warehouse
    add column `after_sale_staff_str` varchar(300) default '' comment '售后客服人员ID，逗号分割';


CREATE TABLE `after_sale_share_link`
(
    `id`               bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`       bigint     DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`      bigint     DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`       bigint     DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`      bigint     DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`           tinyint    DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`       bigint               NULL COMMENT '删除时间',
    `status`           tinyint(2)           not null default 0 comment '0正常，1禁用',
    `validity_period`  int(10)              not null default 0 comment '有效期，单位天',
    `always_validity`  tinyint(2)           not null default 0 comment '0存在有效期，1长期有效',
    `no`               varchar(200)         not null comment '链接编码',
    `name`             varchar(100)         not null comment '链接名称',
    `link_params`      json comment '分享链接参数',
    `link_val`         text                 null comment '链接',
    expired_time_point bigint(13) default null comment '过期时间',
    index idx_no (`no`),
    index idx_name (`name`)
) comment '售后分享链接信息';


CREATE TABLE `after_sale_share_link_user`
(
    `id`          bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`  bigint  DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid` bigint  DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`  bigint  DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid` bigint  DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`      tinyint DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`  bigint            NULL COMMENT '删除时间',
    `name`        varchar(20)       not null default '' comment '邀请人',
    `tel`         bigint(20)        null comment '邀请人手机号码',
    `link_id`     bigint(30)        not null comment '售后分享链接ID',
    INDEX idx_tel (`tel`),
    index idx_link_id (link_id)
) comment '售后分享链接邀请用户信息';



CREATE TABLE `external_user`
(
    `id`          bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`  bigint DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid` bigint DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`  bigint DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid` bigint DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`      bigint DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`  bigint           NULL COMMENT '删除时间',
    `name`        varchar(20)      not null default '' comment '用户名称',
    `tel`         bigint(20)       null comment '手机号码',
    status        tinyint          not null default 0 comment '0 正常。1禁用',
    unique key tel__uindex (tel, is_del)
) comment '外部账号管理';


CREATE TABLE `after_sale_forwarding_register`
(
    `id`                     bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`             bigint DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`            bigint DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`             bigint DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`            bigint DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`                 bigint DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`             bigint           NULL COMMENT '删除时间',
    `delivery_no`            varchar(64)      not null comment '快递单号',
    `status`                 tinyint          not null default 0 comment '处理状态 0:待处理 1:待转寄 2:已转寄 3:无需转寄',
    `item_name`              varchar(255)     null comment '商品名称',
    `item_num`               int              not null default 0 comment '商品数量',
    `sku_code`               varchar(255)     null comment '商品SKU',
    `sku_name`               varchar(255)     null comment '规格名称',
    `intact`                 tinyint unsigned null comment '是否完好',
    `abnormal_description`   varchar(500)     not null default '' comment '异常说明',
    `affects_sales_vouchers` json comment '影响销售凭证',
    `order_no`               varchar(255)     null comment '原始单号',
    `stockout_no`            varchar(255)     null comment '出库单号',
    `stockout_warehouse`     varchar(255)     null comment '出库仓库',
    `stockout_warehouse_no`  varchar(255)     null comment '出库仓库编号',
    `forwarding_address`     varchar(500)     null comment '转寄地址',
    `forwarding_delivery_no` varchar(64)      null comment '转寄单号',
    `weight`                 varchar(255)     null comment '重量',
    `carton_model`           varchar(255)     null comment '纸箱型号',
    index delivery_no__index (delivery_no),
    index status__index (status),
    index stockout_warehouse_no__index (stockout_warehouse_no),
    index stockout_no__index (stockout_no),
    index created_at__index (created_at),
    fulltext item_name__findex (item_name) with parser ngram,
    index order_no__index (order_no),
    index sku_no__index (sku_code)
) comment '售后转寄登记';


alter table export_task
    add external_user_id bigint comment '外部用户ID';


ALTER TABLE `operate_log`
    ADD `operator_type` tinyint NOT NULL DEFAULT 0 COMMENT '操作人类型 0:内部员工 1:外部用户'
;

ALTER TABLE `wdt_order` add index logistics_no__index(`logistics_no`);
CREATE TABLE `shop_operator_map`
(
    `id`           BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`   BIGINT DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`  BIGINT DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`   BIGINT DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`  BIGINT DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`       BIGINT DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`   BIGINT DEFAULT 0 NOT NULL COMMENT '删除时间',
    `shop_id`      BIGINT(13)       NOT NULL COMMENT '店铺ID',
    `operator_uid` BIGINT(13)       NOT NULL COMMENT '运营负责人ID',
    UNIQUE `uniq_shop_id` (`shop_id`)
) COMMENT '下架管理-店铺/运营映射关联';

CREATE TABLE `off_shelf_info`
(
    `id`              BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`      BIGINT       DEFAULT 0   NOT NULL COMMENT '创建时间createAt',
    `created_uid`     BIGINT       DEFAULT 0   NOT NULL COMMENT '创建人updateUser',
    `updated_at`      BIGINT       DEFAULT 0   NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`     BIGINT       DEFAULT 0   NOT NULL COMMENT '更新人updateUser',
    `is_del`          BIGINT       DEFAULT 0   NOT NULL COMMENT '是否已删除',
    `deleted_at`      BIGINT       DEFAULT 0   NOT NULL COMMENT '删除时间',
    `urgent_level`    INT(2)                   NOT NULL COMMENT '紧急程度.1十分。2紧张。3一般',
    `reason_type`     VARCHAR(50)              NOT NULL COMMENT '下架理由（多选，英文逗号分割）。0店铺销量，1客诉问题，2检测不合格，3舆情问题，4商务合作问题，5其他',
    `reason_txt`      VARCHAR(800)             NOT NULL COMMENT '下架理由',
    `reason_file`     TEXT                     NULL COMMENT '下架理由（文件）',
    `no`              VARCHAR(50)              NOT NULL COMMENT '下架流程编码',
    `status`          INT(2)       DEFAULT 0   NOT NULL COMMENT '下架流程状态。0待提交，1待审核，11已撤回，2待处理，3已完成，31已拒绝',
    `shop_id`         VARCHAR(200) DEFAULT '*' NOT NULL COMMENT '店铺ID，英文逗号分割，默认*，代表所有店铺',
    `select_all`      tinyint      default 0   not null comment '是否全选',
    `operator_uid`    VARCHAR(300) DEFAULT ''  NOT NULL COMMENT '运营人员ID，英文逗号分割',
    `approve_remark`  VARCHAR(1000) DEFAULT ''  NOT NULL COMMENT '审核备注',
    `submit_time`     BIGINT       DEFAULT 0   NOT NULL COMMENT '提交时间',
    `submit_uid`      BIGINT       DEFAULT 0   NOT NULL COMMENT '提交人ID',
    `process_inst_id` VARCHAR(64)  DEFAULT ''  NOT NULL COMMENT '流程实例ID',
    `approve_time`    BIGINT       DEFAULT 0   NOT NULL COMMENT '审核时间',
    `approve_uid`     BIGINT       DEFAULT 0   NOT NULL COMMENT '审核人ID',
    UNIQUE `uniq_no` (`no`, `is_del`)
) COMMENT '下架管理-下架流程信息';

CREATE TABLE `off_shelf_item`
(
    `id`                BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`       BIGINT DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`       BIGINT DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`            BIGINT DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '删除时间',
    `off_shelf_info_id` BIGINT(13)       NOT NULL COMMENT '下架流程信息ID',
    `item_id`           BIGINT(2)        NOT NULL COMMENT '商品ID',
    `item_code`         VARCHAR(50)      NOT NULL COMMENT '商品编码',
    `remark`            TEXT             NULL COMMENT '备注说明',
    INDEX `idx_off_shelf_info_id` (`off_shelf_info_id`)
) COMMENT '下架管理-下架流程商品信息';

CREATE TABLE `off_shelf_feedback`
(
    `id`                BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`       BIGINT DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`       BIGINT DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`            BIGINT DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`        BIGINT DEFAULT 0 NOT NULL COMMENT '删除时间',
    `off_shelf_info_id` BIGINT(13)       NOT NULL COMMENT '下架流程信息ID',
    `process_uid`       BIGINT(13)       NOT NULL COMMENT '处理人ID',
    `task_id`           VARCHAR(64)      NULL COMMENT '任务ID',
    `feedback`          INT    DEFAULT 0 NOT NULL COMMENT '下架反馈 1已下架 2未下架 3没有上架',
    `link`              VARCHAR(250)     NULL COMMENT '下架链接',
    `remark`            TEXT             NULL COMMENT '备注说明',
    INDEX `idx_off_shelf_info_id` (`off_shelf_info_id`)
) COMMENT '下架管理-下架反馈';


-- 消息模板
-- @formatter:off
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (46, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_TO_APPROVE', '下架商品申请', 'ERP系统—下架商品申请【${紧急程度}】', '${发起人花名} 提交时间:${提交时间}【发起】${商品名称} 下架申请，请及时查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (47, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_REVOKED', '下架商品申请撤回', 'ERP系统—下架商品申请撤回【${紧急程度}】', '${发起人花名} 撤回时间:${提交时间}【撤回】${商品名称} 下架申请，请及时查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (48, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_TO_PROCESS', '下架商品审核通过', 'ERP系统—下架商品审核【${紧急程度}】', '${审核人花名}【同意下架】${发起人花名} 提交时间：${提交时间} 的下架商品流程，请及时查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (49, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_REFUSED', '下架商品审核拒绝', 'ERP系统—下架商品审核【${紧急程度}】', '${审核人花名}【拒绝下架】${发起人花名} 提交时间：${提交时间} 的下架商品流程，请及时查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (50, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_FINISHED', '下架商品运营处理完成', 'ERP系统—下架商品运营处理完成【${紧急程度}】', '下架运营【已处理】${发起人花名} 提交时间:${提交时间} 提交 ${商品名称} 流程，请及时查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');
INSERT INTO supplier.msg_template (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, status, code, name, title, content, receiver, link, remark) VALUES (51, 1733985786, 0, 0, 0, 0, 0, 1, 'OFF_SHELF_URGE', '下架商品催办', 'ERP系统—下架商品催办【${紧急程度}】', '${催办花名} 催您尽快审核或处理下架商品流程，请立即查看！', '${接收者}', '${sys.domain}/operation-management/off-shelf/detail?id=${ID}', '下架流程');



ALTER TABLE shop_inventory
    ADD COLUMN `inventory_mode` tinyint DEFAULT 0 NOT NULL COMMENT '库存模式 0 共享, 1 锁定'
;
ALTER TABLE `shop_inventory_setting`
    ADD COLUMN `inventory_mode` tinyint DEFAULT 0 NOT NULL COMMENT '库存模式 0 共享, 1 锁定'
;

ALTER TABLE `virtual_warehouse`
    ADD COLUMN `inventory_mode` tinyint DEFAULT 0 NOT NULL COMMENT '库存模式 0 共享, 1 锁定'
;

ALTER TABLE `virtual_warehouse_inventory`
    ADD COLUMN `inventory_mode` tinyint DEFAULT 0 NOT NULL COMMENT '库存模式 0 共享, 1 锁定'
;

ALTER TABLE `sys_inventory_setting`
    ADD COLUMN `wdt_sync_frequency` bigint DEFAULT 0 NOT NULL COMMENT '旺店通仓内库存的同步频次（单位秒，实时设置为0即可）'
;

ALTER TABLE `virtual_warehouse_inventory_goods`
    ADD COLUMN `inventory_ratio2`   tinyint DEFAULT 0 NOT NULL COMMENT '排除被锁定的库存占比后换算的占比',
    ADD COLUMN `inventory_lock_num` int     DEFAULT 0 NOT NULL COMMENT '库存锁定数量'
;

ALTER TABLE `shop_inventory_goods`
    ADD COLUMN `inventory_ratio2`   tinyint DEFAULT 0 NOT NULL COMMENT '排除被锁定的库存占比后换算的占比',
    ADD COLUMN `inventory_lock_num` int     DEFAULT 0 NOT NULL COMMENT '库存锁定数量'
;

CREATE TABLE `warehouse_goods_inventory_lock_statics`
(
    `id`           bigint AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    `created_at`   bigint DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid`  bigint DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`   bigint DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`  bigint DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`       bigint DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`   bigint           NULL COMMENT '删除时间',
    `warehouse_no` varchar(200)     NOT NULL COMMENT '仓库编码',
    `sku_no`       varchar(100)     NOT NULL COMMENT 'SKU编码',
    `lock_num`     int              NOT NULL COMMENT '库存锁定数量',
    `lock_ratio`   int              NOT NULL COMMENT '库存锁定占比',
    `version`      int(8) DEFAULT 1 NOT NULL COMMENT '数据版本号',
    CONSTRAINT `sku_uindex` UNIQUE (`warehouse_no`, `sku_no`, `is_del`)
) COMMENT '库存锁定数量统计'
;

#复制一张旺店通实时库存表（rt:real time）
create table wdt_stock_spec_rt like wdt_stock_spec;

ALTER TABLE `wdt_stock_spec_rt`
    ADD INDEX `updated_at__index` (`updated_at`)
;
ALTER TABLE `wdt_stock_spec`
    ADD INDEX `updated_at__index` (`updated_at`)
;

ALTER TABLE `platform_item_sku_inventory_setting`
    MODIFY `outer_item_id` varchar(64) NOT NULL COMMENT '外部平台商品ID',
    MODIFY `outer_sku_id` varchar(64) NOT NULL COMMENT '外部平台商品SKU ID',
    ADD COLUMN `sku_code`         varchar(64) DEFAULT '' NOT NULL COMMENT 'SKU编码',
    ADD COLUMN `combination_code` varchar(64) DEFAULT '' NOT NULL COMMENT '组合装编码',
    MODIFY `is_del` bigint DEFAULT 0 NOT NULL COMMENT '是否已删除',
    ADD CONSTRAINT `uk`
        UNIQUE (`shop_no`, `outer_item_id`, `outer_sku_id`, `sku_code`, `combination_code`, `is_del`),
    DROP KEY `shop_no_outer_item_sku_id`,
    ADD INDEX shop_sku_code__index (shop_no, sku_code, is_del)
;

CREATE INDEX idx_outer_sku_code
    ON platform_item_sku (outer_sku_code)
    COMMENT '索引（外部SKU编码）';

ALTER TABLE sys_inventory_setting
    MODIFY sync_after_modify_radio BIGINT DEFAULT 0 NOT NULL COMMENT '修改占比后是否需要实时同步库存（废弃）';



ALTER TABLE `shop_inventory` MODIFY `inventory_ratio` DECIMAL(9, 6) NOT NULL COMMENT '库存占比';
ALTER TABLE `shop_inventory_goods`
    MODIFY `inventory_ratio` DECIMAL(9, 6) NOT NULL COMMENT '库存占比',
    MODIFY `inventory_ratio2` DECIMAL(9, 6) DEFAULT 0 NOT NULL COMMENT '排除被锁定的库存占比后换算的占比';

ALTER TABLE `virtual_warehouse_inventory` MODIFY `inventory_ratio` DECIMAL(9, 6) NOT NULL COMMENT '库存占比';
ALTER TABLE `virtual_warehouse_inventory_goods`
    MODIFY `inventory_ratio` DECIMAL(9, 6) NOT NULL COMMENT '库存占比',
    MODIFY `inventory_ratio2` DECIMAL(9, 6) DEFAULT 0 NOT NULL COMMENT '排除被锁定的库存占比后换算的占比';

ALTER TABLE `warehouse`
    MODIFY `inventory_ratio` DECIMAL(9, 6) DEFAULT 0 NOT NULL COMMENT '库存占比';

ALTER TABLE `warehouse_goods_inventory_statics` MODIFY `inventory_ratio` DECIMAL(9, 6) NOT NULL COMMENT '库存占比';
ALTER TABLE `warehouse_goods_inventory_lock_statics` MODIFY     `lock_ratio`   DECIMAL(9, 6)               NOT NULL COMMENT '库存锁定占比';

ALTER TABLE `platform_item_inventory_setting` MODIFY `inventory_ratio` DECIMAL(9, 6) DEFAULT 100 NOT NULL COMMENT '库存占比';
ALTER TABLE `platform_item_sku_inventory_setting` MODIFY `inventory_ratio` DECIMAL(9, 6) DEFAULT 100 NOT NULL COMMENT '库存占比';

ALTER TABLE `supplier`.`item_sku`
    ADD COLUMN `contract_sale_price` DECIMAL(20, 2) NULL COMMENT '合同销售价';

ALTER TABLE `supplier`.`item`
    ADD `business_lines`    VARCHAR(32) DEFAULT 0 NULL COMMENT '0电商 1老爸抽检 2绿色家装 3商家入驻',
    ADD `is_business_line0` TINYINT GENERATED ALWAYS AS (FIND_IN_SET('0', `business_lines`) != 0) COMMENT '是否属于电商',
    ADD INDEX `is_business_line0__index` (`is_business_line0`),
    ADD `is_business_line1` TINYINT GENERATED ALWAYS AS (FIND_IN_SET('1', `business_lines`) != 0) COMMENT '是否属于老爸抽检',
    ADD INDEX `is_business_line1__index` (`is_business_line1`),
    ADD `is_business_line2` TINYINT GENERATED ALWAYS AS (FIND_IN_SET('2', `business_lines`) != 0) COMMENT '是否属于绿色家装',
    ADD INDEX `is_business_line2__index` (`is_business_line2`),
    ADD `is_business_line3` TINYINT GENERATED ALWAYS AS (FIND_IN_SET('3', `business_lines`) != 0) COMMENT '是否属于商家入驻',
    ADD INDEX `is_business_line3__index` (`is_business_line3`);

CREATE TABLE `supplier`.`entry_activity_price_item`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`  BIGINT  DEFAULT 0  NOT NULL COMMENT '创建时间',
    `created_uid` BIGINT  DEFAULT 0  NOT NULL COMMENT '创建人',
    `updated_at`  BIGINT  DEFAULT 0  NOT NULL COMMENT '更新时间',
    `updated_uid` BIGINT  DEFAULT 0  NOT NULL COMMENT '更新人',
    `is_del`      BIGINT  DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`  BIGINT             NULL COMMENT '删除时间',
    `month`       CHAR(7)            NOT NULL COMMENT '所属年月(2024-10)',
    `provider_id` BIGINT             NOT NULL COMMENT '供应商id',
    `item_id`     BIGINT             NOT NULL COMMENT '商品id',
    `item_code`   VARCHAR(32)        NOT NULL COMMENT '商品编码',
    `status`      TINYINT DEFAULT -1 NOT NULL COMMENT '状态 -1 待发起 0 待确认 1 已确认 2 存在异议',
    `confirm_id`  BIGINT  DEFAULT 0  NOT NULL COMMENT '确认ID'
) COMMENT '入驻活动价格(商品纬度)';

CREATE TABLE `supplier`.`entry_activity_price_sku`
(
    `id`                  BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`          BIGINT         DEFAULT 0    NOT NULL COMMENT '创建时间',
    `created_uid`         BIGINT         DEFAULT 0    NOT NULL COMMENT '创建人',
    `updated_at`          BIGINT         DEFAULT 0    NOT NULL COMMENT '更新时间',
    `updated_uid`         BIGINT         DEFAULT 0    NOT NULL COMMENT '更新人',
    `is_del`              BIGINT         DEFAULT 0    NOT NULL COMMENT '是否已删除',
    `deleted_at`          BIGINT                      NULL COMMENT '删除时间',
    `price_item_id`       BIGINT                      NOT NULL COMMENT '入驻活动价格商品id',
    `active_start`        BIGINT         DEFAULT 0    NOT NULL COMMENT '活动时间(归属时间)',
    `active_end`          BIGINT         DEFAULT 0    NOT NULL COMMENT '活动时间(归属时间)',
    `item_id`             BIGINT                      NOT NULL COMMENT '商品id',
    `item_code`           VARCHAR(32)                 NOT NULL COMMENT '商品编码',
    `sku_id`              BIGINT                      NOT NULL COMMENT 'sku id',
    `sku_code`            VARCHAR(32)                 NOT NULL COMMENT 'sku编码',
    `contract_sale_price` DECIMAL(20, 2)              NOT NULL COMMENT '合同销售价',
    `platform_commission` DECIMAL(20, 2) DEFAULT 0.00 NULL COMMENT '平台佣金',
    `activity_remark`     VARCHAR(500)   DEFAULT NULL COMMENT '活动备注'
) COMMENT '入驻活动价格(SKU纬度)';

CREATE TABLE `supplier`.`entry_activity_price_confirm`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`  BIGINT DEFAULT 0 NOT NULL COMMENT '创建时间',
    `created_uid` BIGINT DEFAULT 0 NOT NULL COMMENT '创建人',
    `updated_at`  BIGINT DEFAULT 0 NOT NULL COMMENT '更新时间',
    `updated_uid` BIGINT DEFAULT 0 NOT NULL COMMENT '更新人',
    `is_del`      BIGINT DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`  BIGINT           NULL COMMENT '删除时间',
    `provider_id` BIGINT           NOT NULL COMMENT '供应商ID',
    `short_link`  VARCHAR(32)      NOT NULL COMMENT '短链接'
) COMMENT '入驻活动价格确认';
CREATE TABLE `supplier`.`entry_activity_price_dissent`
(
    `id`             BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`     BIGINT       DEFAULT 0 NOT NULL COMMENT '创建时间',
    `created_uid`    BIGINT       DEFAULT 0 NOT NULL COMMENT '创建人',
    `updated_at`     BIGINT       DEFAULT 0 NOT NULL COMMENT '更新时间',
    `updated_uid`    BIGINT       DEFAULT 0 NOT NULL COMMENT '更新人',
    `is_del`         BIGINT       DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`     BIGINT                 NULL COMMENT '删除时间',
    `price_item_id`  BIGINT                 NOT NULL COMMENT '入驻活动价格商品id',
    `dissent_reason` VARCHAR(500) DEFAULT NULL COMMENT '异议说明'
) COMMENT '入驻活动价格异议';

ALTER TABLE `supplier`.`entry_activity_price_item`
    ADD INDEX idx_month(`month`),
    ADD INDEX idx_provider_id(`provider_id`),
    ADD INDEX idx_status(`status`),
    ADD INDEX idx_item_code(`item_code`),
    ADD INDEX idx_confirm_id(`confirm_id`);

ALTER TABLE `supplier`.`entry_activity_price_sku`
    ADD INDEX idx_price_item_id(`price_item_id`),
    ADD INDEX idx_item_code(`item_code`),
    ADD INDEX idx_sku_code(`sku_code`),
    ADD INDEX idx_active_start(`active_start`),
    ADD INDEX idx_active_end(`active_end`);

ALTER TABLE `supplier`.`entry_activity_price_dissent`
    ADD INDEX idx_price_item_id(`price_item_id`);
ALTER TABLE `supplier`.`buyer`
    ADD `third_id` varchar(255) DEFAULT '' NOT NULL COMMENT '第三方采购员信息';
ALTER TABLE `supplier`.`buyer`
    ADD `type` int(2) DEFAULT '0' null COMMENT '第三方平台列别。0:金蝶。1:班牛';


CREATE TABLE `item_banniu_ref`
(
    `id`         int         NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `created_at` int         NOT NULL COMMENT '创建时间',
    `updated_at` int         NOT NULL COMMENT '修改时间',
    `is_del`     tinyint     NOT NULL DEFAULT 0 COMMENT '是否逻辑删除',
    `deleted_at` int         NOT NULL COMMENT '删除时间',
    `banniu_id`  varchar(64) NOT NULL COMMENT '班牛ID',
    `type`       tinyint     NOT NULL DEFAULT 1 COMMENT '类型 1后端商品 2组合装商品',
    `first_id`   varchar(64) NOT NULL COMMENT '商品ID',
    `second_id`  varchar(64) NOT NULL COMMENT '商品ID（二级ID）',
    INDEX `idx_id` (`type`, `first_id`, `second_id`)
) COMMENT '班牛数据关联记录'
;
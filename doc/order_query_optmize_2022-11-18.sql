ALTER TABLE `wdt_refund_order`
    ADD `platform_id` INT NULL COMMENT '平台id，见附件'
;

UPDATE `wdt_refund_order`
SET `wdt_refund_order`.`platform_id` = (SELECT `wdt_refund_order_detail`.`platform_id`
                                        FROM `wdt_refund_order_detail`
                                        WHERE `wdt_refund_order_detail`.`refund_no` =
                                              `wdt_refund_order`.`refund_no`
                                          AND `wdt_refund_order_detail`.`deleted_at` = 0
                                        LIMIT 1)
WHERE `wdt_refund_order`.`platform_id` IS NULL
;

CREATE INDEX `idx_platform_id`
    ON `wdt_refund_order` (`platform_id`)
;
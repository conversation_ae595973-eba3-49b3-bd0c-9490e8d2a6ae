# 上新计划商品列表新增字段
ALTER TABLE `item_launch_plan_item_ref`
    ADD `active_period_start` BIGINT(13)   DEFAULT NULL COMMENT '新品活动开始时间',
    ADD `active_period_end`   BIGINT(13)   DEFAULT NULL COMMENT '新品活动结束时间',
    ADD `share_disk_link`     VARCHAR(200) DEFAULT '' COMMENT '共享盘链接';

# 商品上新模块审核表新增【内容识别状态】
ALTER TABLE `item_drawer_module_audit`
    ADD `recognition_status` TINYINT(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '敏感词识别状态 0:识别中 1:已完成' AFTER `audit_status`
;

# 新增【商品抽屉内容识别审核任务】表
CREATE TABLE `item_drawer_recognition_task`
(
    `id`              BIGINT AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`      BIGINT       DEFAULT 0 NOT NULL COMMENT '创建时间',
    `created_uid`     BIGINT       DEFAULT 0 NOT NULL COMMENT '创建人',
    `updated_at`      BIGINT       DEFAULT 0 NOT NULL COMMENT '更新时间',
    `updated_uid`     BIGINT       DEFAULT 0 NOT NULL COMMENT '更新人',
    `is_del`          TINYINT      DEFAULT 0 NOT NULL COMMENT '是否删除',
    `module`          VARCHAR(64)            NOT NULL COMMENT '模块',
    `field`           VARCHAR(64)            NOT NULL COMMENT '识别字段',
    `item_id`         BIGINT                 NOT NULL COMMENT '商品id',
    `round`           INT UNSIGNED DEFAULT 1 NOT NULL COMMENT '当前是第几轮审核',
    `drawer_image_id` BIGINT                 NOT NULL COMMENT '抽屉图片id',
    `image_url`       VARCHAR(512)           NOT NULL DEFAULT '' COMMENT '抽屉图片URL',
    `image_type`      SMALLINT     DEFAULT 0 NOT NULL COMMENT '抽屉图片类型 1-抽屉商品 2-抽屉详情页 3-规格图片 4-主图视频 5-属性图片',
    `status`          TINYINT                NOT NULL DEFAULT 0 COMMENT '状态 0 失败 1 准备 2 等待敏感词检查 3 敏感词检测完成',
    `content`         MEDIUMTEXT COMMENT '敏感词检查内容',
    `cv_result`         MEDIUMTEXT COMMENT '图片识别结果',
    `hit_result`      MEDIUMTEXT COMMENT '敏感词命中结果（JSON）',
    `log`             TEXT COMMENT '日志'
) COMMENT '商品抽屉内容识别审核任务'
;

#新品商品库新增字段
ALTER TABLE `new_goods`
    ADD `single_buy_price` DECIMAL(20, 2) DEFAULT NULL COMMENT '单买到手价';

ALTER TABLE `provider`
    ADD `main_charger_user_id` BIGINT(13) DEFAULT null COMMENT '负责人用户id',
    ADD `second_charger_user_id`   BIGINT(13) DEFAULT null COMMENT '次要负责人用户id';

ALTER TABLE `item_drawer_recognition_task`
    ADD `fail_count` TINYINT NOT NULL DEFAULT 0 COMMENT '失败次数';
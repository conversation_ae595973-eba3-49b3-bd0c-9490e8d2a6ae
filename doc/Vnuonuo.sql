-- auto-generated definition
create table all_channel_bill_data
(
    id                 bigint(30) auto_increment comment 'id'
        primary key,
    order_id           varchar(100) default ''                not null comment '订单编号',
    sub_order_id       varchar(100) default ''                not null comment '子订单号',
    goods_id           varchar(30)                            null comment '平台商品ID',
    plat_name          varchar(10)                            null comment '平台',
    shop_name          varchar(20)                            null comment '店铺名称',
    ctime              bigint                                 null comment '下单时间',
    pay_time           bigint                                 null comment '支付时间',
    order_status       int                                    null comment '订单状态，0:未知,1:待支付,2:定金已支付,3:已支付,4:部分已发货,5:全部已发货,6:已完成,7:已取消',
    goods_name         varchar(100)                           null comment '商品名称',
    sku_code           varchar(30)  default ''                not null comment 'sku编码',
    spec               varchar(100)                           null comment '规格型号',
    unit               varchar(10)                            null comment '单位',
    goods_num          int                                    null comment '商品数量',
    unit_price         decimal(19, 4)                         null comment '商品单价',
    total_amount       decimal(19, 4)                         null comment '总金额',
    discount_amt       decimal(19, 4)                         null comment '优惠折扣金额',
    post_fee           decimal(19, 4)                         null comment '运费',
    rfd_amount         decimal(19, 4)                         null comment '售后金额',
    tax_class_code     varchar(20)                            null comment '商品和服务税收分类编码',
    tax_rate           decimal(19, 4)                         null comment '税率',
    real_amount        decimal(19, 4)                         null comment '实付金额',
    invoice_amt        decimal(19, 4)                         null comment '开票金额（实付-售后）',
    tax_amount         decimal(19, 4)                         null comment '税额',
    invoice_norate     decimal(19, 4)                         null comment '不含税开票金额',
    create_time        timestamp    default CURRENT_TIMESTAMP null comment '创建时间',
    update_time        timestamp    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    status             tinyint(2)   default 0                 null comment '状态',
    is_refund          tinyint(2)   default 0                 null comment '1 退款中。0没有',
    amount_refresh     tinyint                                null comment '金额刷新，0：否。1是。默认0',
    is_del             tinyint(1)   default 0                 null comment '逻辑删除标示',
    refund_refresh     tinyint(1)   default 0                 null comment '退款状态是否刷新。否,1:是，默认0',
    error_msg          text                                   null comment '错误信息',
    refund_finish_time bigint(10)   default 0                 null comment '退款完成时间',
    index idx_order_id (order_id),
    unique biz_data (order_id, sub_order_id, sku_code)
)
    comment '全渠道开票数据';

# create index idx_del_amount_refresh_refund_create_time
#     on all_channel_bill_data (is_del, create_time, amount_refresh, is_refund, refund_refresh);
#
# create index idx_del_refund_create_time
#     on all_channel_bill_data (is_del, is_refund, create_time);


create table `nuo_nuo_invoice_request`
(
    `id`               bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    order_no           varchar(512)             not null comment '订单编号',
    invoice_type       tinyint(2)    default 0  not null comment '发票类型',
    invoice_title_type tinyint(2)    default 0  not null comment '发票抬头类型',
    invoice_title      varchar(1024) default '' not null comment '发票抬头',
    tax_code           varchar(1024) default '' not null comment '税号',
    bank               varchar(1024) default '' not null comment '开户银行',
    bank_no            varchar(1024) default '' not null comment '开户银行账号',
    company_address    varchar(2048) default '' not null comment '公司名称',
    company_phone      varchar(1024) default '' not null comment '企业电话',
    mail_address       varchar(1024) default '' not null comment '邮箱',
    `created_at`       bigint        DEFAULT 0  NOT NULL COMMENT '创建时间createAt',
    `created_uid`      bigint        DEFAULT 0  NOT NULL COMMENT '创建人updateUser',
    `updated_at`       bigint        DEFAULT 0  NOT NULL COMMENT '更新时间updateAt',
    `updated_uid`      bigint        DEFAULT 0  NOT NULL COMMENT '更新人updateUser',
    `is_del`           bigint        DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`       bigint        DEFAULT 0  NOT NULL COMMENT '删除时间',
    index idx_order_no (order_no)
) COMMENT '用户诺诺开票请求参数';

# alter table `nuo_nuo_invoice_request`
#     add column `status` tinyint(1) default 0 not null comment '请求状态';

create table nuo_nuo_invoice_process_record
(
    `id`          bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    order_no      varchar(512)         not null comment '订单编号',
    status        tinyint(2) default 0 not null comment '状态',
    record        text                 null comment '记录',
    `created_at`  bigint     DEFAULT 0 NOT NULL COMMENT '创建时间createAt',
    `created_uid` bigint     DEFAULT 0 NOT NULL COMMENT '创建人updateUser',
    `updated_at`  bigint     DEFAULT 0 NOT NULL COMMENT '更新时间updateAt',
    `updated_uid` bigint     DEFAULT 0 NOT NULL COMMENT '更新人updateUser',
    `is_del`      bigint     DEFAULT 0 NOT NULL COMMENT '是否已删除',
    `deleted_at`  bigint     DEFAULT 0 NOT NULL COMMENT '删除时间',
    index idx_order_no (order_no)
) COMMENT '用户诺诺开票流程记录';
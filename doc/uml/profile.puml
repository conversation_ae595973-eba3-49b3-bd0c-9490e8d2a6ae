@startuml
'https://plantuml.com/class-diagram
skinparam linetype ortho

package CategoryAggregation {
    class Category {
        - Long id
        - String name
        - Long rootId
        - String path
        - Long parentId
        - Integer level
        + List<categoryAttr> getAttrs()
        + categoryAttr getAttr(String name)
        + void delAttr(String name)
    }
    class CategoryAttr {
        - Long id
        - Long categoryId
        - String name
        - Integer isRequired
    }
}

package BrandAggregation {
    class Brand {
        - Long id
        - String sn
        - String name
        - String logo
        - Long providerId
        - Integer status
    }
}

package ProviderAggregation {
    class Provider {
        - Long id
        - String sn
        - String name
        - String contact
        - String contactMobile
        - String unifySocialCreditCodes
        - String provinceCode
        - String cityCode
        - String areaCode
        - String address
        - Integer status
        - Integer type
        - Long partnerProviderId
    }
}

package ShopAggregation {
    class Shop {
        - Long id
        - String account
        - String link
        - String head
        - String logo
        - Integer platform
        - Integer status
    }
}

package ItemAggregation {
    class Item {
        - Long id
        - String name
        - String partnerProviderItemSn
        - Long categoryId
        - Long brandId
        - String providerSpecifiedCode
        - Integer status
        - String statusRemark
        - Long estimateSaleTime
    }
    class ItemOperator {
        - Long id
        - String customName
        - Long operatorId
        - Long isHead
    }
    class ItemAttr {
        - Long id
        - Long itemId
        - Long attrId
        - Long categoryId
        - String attrValue
    }
    class ItemPrice {
        - Long id
        - Long itemId
        - Integer type
        - Integer isMain
        - BigDecimal price
        - String customName
        - String remark
        - Integer isLongTerm
        - Long startTime
        - Long endTime
    }
    class ItemImage {
        - Long id
        - Long itemId
        - String imageUrl
        - Integer type
        - Integer isMain
        - Integer sort
    }
    class ItemProcurement {
        - Long id
        - Long itemId
        - Long providerId
        - Long buyerId
        - Integer isGift
        - Long mainItemId
        - Integer isWarehouseDelivery
        - Integer isFactoryDelivery
    }
    class ItemExpressTemplate {
        - Long id
        - Long itemId
        - String from
        - String expressCompany
        - String freeArea
        - String chargeArea
        - String remark
    }
    class ItemSku {
        - Long id
        - Long itemId
        - String providerSpecifiedCode
        - List<itemAttr> getAttrs();
    }
}

package OperateLogAggregation {
    class OperateLog {
        - Long id
        - Integer targetType
        - Long targetId
        - String msg
        - String data
    }
}

package Infrastructure {
    class EventBus {
        + String publishEvent(Event event)
        + void subscribeEvent(Callback callback)
    }
    note left: 事件总线
}

package ItemSyncAggregation {
    class ItemSyncListener {
        + void listen(Event event)
    }
    note right: 监听商品变更事件
    class ItemSyncDomainService {
        + Long syncItem(Item item)
    }
    interface ItemSyncGateway {
        + (abstract)Long syncItem(Item item)
    }
    note right of ItemSyncGatewayKindgeeImpl : 商品同步网关金蝶实现
    class ItemSyncGatewayKindgeeImpl {
        + Long syncItem(Item item)
    }
    note right of ItemSyncGatewayWdtImpl : 商品同步网关旺店通实现（预留）
    class ItemSyncGatewayWdtImpl {
        + Long syncItem(Item item)
    }
    class ItemSyncLog {
        - Long id
        - Long itemId
        - Long total
        - Long processing
        - Long success
        - Long fail
        - Long status
    }
    class ItemSyncLogDetail {
        - Long id
        - Long itemId
        - Long itemSyncLogId
        - Integer syncTarget
        - Long status
        - Long failNum
        - Long failMsg
        - Long successTime
    }
}

CategoryAttr --* Category
Item -- Brand
Brand -- Provider
Item *-- ItemSku
Item *-- ItemAttr
Item *-- ItemExpressTemplate
Item *-- ItemImage
Item *-- ItemOperator
Item *-- ItemPrice
Item *-- ItemProcurement
Item -- Category
ItemAttr -- CategoryAttr

ItemSyncListener - EventBus
ItemSyncListener - ItemSyncDomainService
ItemSyncDomainService -- ItemSyncGateway
ItemSyncGatewayKindgeeImpl --|> ItemSyncGateway
ItemSyncGatewayWdtImpl --|> ItemSyncGateway







@enduml
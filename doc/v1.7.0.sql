CREATE TABLE `user_visit_daily`
(
    `id`          bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `user_id`     bigint            NOT NULL COMMENT '用户ID',
    `date`        date              NOT NULL COMMENT '访问日期',
    `created_at`  bigint  DEFAULT 0 NOT NULL COMMENT '创建时间',
    `created_uid` bigint  DEFAULT 0 NOT NULL COMMENT '创建人',
    `updated_at`  bigint  DEFAULT 0 NOT NULL COMMENT '更新时间',
    `updated_uid` bigint  DEFAULT 0 NOT NULL COMMENT '更新人',
    `is_del`      tinyint DEFAULT 0 NOT NULL COMMENT '是否删除，不能做业务。这是软删除标记'
) COMMENT '用户每日访问记录'
;

CREATE UNIQUE INDEX `user_id_date__uindex` ON `user_visit_daily` (`user_id`, `date`)
;

ALTER TABLE `item_drawer`
    ADD `dou_link` varchar(200) NOT NULL DEFAULT '' COMMENT '抖音链接',
    ADD `tb_link`  varchar(200) NOT NULL DEFAULT '' COMMENT '淘宝链接'
;

ALTER TABLE `item`
    ADD `audit_status` tinyint UNSIGNED DEFAULT 0 NOT NULL COMMENT '商品审核状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成'
;

UPDATE `item` SET item.`audit_status` = 100 WHERE `launch_status` > 4;
UPDATE `item` SET item.`audit_status` = 5 WHERE `launch_status` = 4;

ALTER TABLE `aws_approval_node`
    ADD `version` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '版本控制'
;

INSERT INTO `supplier`.`aws_approval_node` (`id`, `process_def_id`, `type`, `created_at`, `created_uid`, `updated_at`,
                                            `updated_uid`, `is_del`, `deleted_at`, `version`)
VALUES (NULL, 'obj_3cdd4598a0004c74989acc8557f3310d', 5, unix_timestamp(), NULL, NULL, NULL, 0, NULL, 1)
;
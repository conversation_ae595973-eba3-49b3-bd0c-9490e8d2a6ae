ALTER TABLE `item`
    ADD business_line int(2) default 0 COMMENT '业务线';

-- 业务线字段初始化。全部改为电商。
update item
set business_line = 0
where id > 0;

-- 更新业务线为绿色家装
update item
set business_line = 2
where id in
      (10630, 10633, 10843, 12958, 13785, 13789, 13790, 13791, 13792, 13793, 13794, 13795, 13797, 13803, 13804, 13806,
       14370, 14390, 14391, 14392, 14904, 14906, 14998, 14999, 15000, 15001, 15002, 15003, 15004, 15005, 15006, 15597,
       15598, 15599, 15600, 17306, 17411, 18145, 18762, 19340, 19341, 19811, 19812, 19813, 19814, 19815, 19816, 19817,
       20211, 20521, 20524, 20588, 20675, 20676, 20677, 20678, 20679, 20680, 20681, 20735, 20736, 20745, 20751, 20817,
       20871, 20872, 41532, 41533, 41534, 43842, 43843, 43844, 43845, 43846, 44691, 47656, 48160, 49040, 49041, 49042,
       49043, 49044, 49182, 49183, 49184, 49185, 49188, 49189, 49190, 49191, 49192, 49193, 49194, 49195, 49196, 49197,
       49524, 51534, 51609, 51610, 51979);









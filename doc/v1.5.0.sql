CREATE TABLE `sale_item_library`
(
    `id`                        BIGINT AUTO_INCREMENT
        PRIMARY KEY COMMENT 'ID',
    `item_sku_code`             VARCHAR(50)          NOT NULL COMMENT '商品SKU编码',
    `member_store_item_id`      VARCHAR(50)          NULL COMMENT '会员店商品ID',
    `applets_item_id`           VARCHAR(50)          NULL COMMENT '小程序商品ID',
    `beauty_store_item_id`      VARCHAR(50)          NULL COMMENT '美妆店商品ID',
    `maternity_baby_item_id`    VARCHAR(50)          NULL COMMENT '母婴店商品ID',
    `woto_buy_item_id`          VARCHAR(50)          NULL COMMENT '小红书商品ID',
    `tik_tok_item_id`           VARCHAR(50)          NULL COMMENT '抖音商品ID',
    `goods_name`                VARCHAR(255)         NULL COMMENT '商品名称',
    `category`                  VARCHAR(255)         NULL COMMENT '行业类目',
    `platform_cost`             VARCHAR(50)          NULL COMMENT '直播/短视频/社群成本',
    `platform_activity_code`    VARCHAR(50)          NULL COMMENT '直播/短视频/社群活动编码',
    `platform_price`            VARCHAR(50)          NULL COMMENT '直播/短视频/社群价格',
    `platform_activity_content` VARCHAR(255)         NULL COMMENT '直播/短视频/社群活动内容',
    `remark`                    VARCHAR(255)         NULL COMMENT '特殊备注（仅限某渠道、库存限量等）',
    `deleted_at`                BIGINT               NULL COMMENT '删除时间',
    `is_del`                    TINYINT(2) DEFAULT 0 NULL COMMENT '是否删除',
    `created_at`                BIGINT               NULL COMMENT '创建时间',
    `created_uid`               BIGINT               NULL COMMENT '创建人',
    `updated_at`                BIGINT               NULL COMMENT '更新时间',
    `updated_uid`               BIGINT               NULL COMMENT '更新人',
    CONSTRAINT `sale_item_library_id_uindex`
        UNIQUE (`id`),
    KEY `index_sale_item_library_item_sku_code_goods_name` (`item_sku_code`, `goods_name`)
)
    COMMENT '销售商品库'
;


CREATE TABLE `new_goods`
(
    `id`                 BIGINT(20)     NOT NULL AUTO_INCREMENT COMMENT 'id',
    `sku_code`           VARCHAR(20)    NOT NULL DEFAULT '' COMMENT 'sku编码',
    `name`               VARCHAR(100)   NOT NULL DEFAULT '' COMMENT '商品名称',
    `specs`              VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '颜色/规格',
    `category_name`      VARCHAR(80)    NOT NULL DEFAULT '' COMMENT '品类名称',
    `brand_name`         VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '品牌名称',
    `shelf_time`         BIGINT(20)     NOT NULL DEFAULT '0' COMMENT '预计上架日期',
    `producer_name`      VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '制作人(运营)',
    `producer_is_search` TINYINT(4)     NOT NULL DEFAULT '1' COMMENT '运营花名是否可以查到 0:查得到 1:查不到',
    `buyer_name`         VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '采购人名称',
    `buyer_is_search`    TINYINT(4)     NOT NULL DEFAULT '1' COMMENT '采购花名是否可以查到 0:查得到 1:查不到',
    `is_expectant`       VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '孕妇能不能用(限日化类产品) 0:可以 1:不能 2:其他',
    `is_sensitive`       VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '敏感肌能不能用(限日化类产品)',
    `age`                VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '几岁以上能吃(限食品类产品)',
    `standard_name`      VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '产品标准名',
    `selling_points`     VARCHAR(10)    NOT NULL DEFAULT '' COMMENT '卖点文案8-10个字',
    `mini_id`            VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '小程序id',
    `membership`         VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '会员店id',
    `beauty_id`          VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '美妆店id',
    `infant_id`          VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '母婴店id',
    `active_period`      VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '新品活动结束周期',
    `is_long_term`       TINYINT                 DEFAULT 0 NOT NULL COMMENT '是否长期有效',
    `no_reason`          VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '是否支持7天无理由退换',
    `line_price`         DECIMAL(20, 2) NOT NULL DEFAULT '0.00' COMMENT '划线价',
    `daily_price`        DECIMAL(20, 2) NOT NULL DEFAULT '0.00' COMMENT '产品日销价',
    `active_price`       DECIMAL(20, 2) NOT NULL DEFAULT '0.00' COMMENT '产品活动价',
    `active_content`     VARCHAR(200)   NOT NULL DEFAULT '' COMMENT '新品活动内容',
    `channel_lowest`     DECIMAL(20, 2) NOT NULL DEFAULT '0.00' COMMENT '渠道活动最低价',
    `live_active`        VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '直播活动机制',
    `is_reduce`          VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '必须参加满减(200-20,最大为199-25)',
    `is_lowest`          VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '以不低于最低价为限,(公司承担折扣)',
    `shipment_type`      VARCHAR(20)    NOT NULL DEFAULT '' COMMENT '仓库/工厂发货',
    `shipment_area`      VARCHAR(50)    NOT NULL DEFAULT '' COMMENT '发货地',
    `shipment_aging`     VARCHAR(10)    NOT NULL DEFAULT '' COMMENT '发货时效',
    `logistics`          VARCHAR(10)    NOT NULL DEFAULT '' COMMENT '物流',
    `express_template`   VARCHAR(200)   NOT NULL DEFAULT '' COMMENT '快递模板',
    `remark`             VARCHAR(200)   NOT NULL DEFAULT '' COMMENT '备注',
    `created_at`         BIGINT(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`        BIGINT(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`         BIGINT(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`        BIGINT(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`             TINYINT(4)     NOT NULL DEFAULT '0' COMMENT '是否删除，不能做业务。这是软删除标记',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='新品商品'
    ENGINE = InnoDB
;

CREATE TABLE `bank_account`
(
    `id`           BIGINT(20)             NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `provider_id`  BIGINT(20)             NOT NULL COMMENT '供应商id',
    `bank_card`    VARCHAR(40)            NOT NULL COMMENT '银行账号',
    `bank_deposit` VARCHAR(255)           NOT NULL COMMENT '开户行',
    `description`  VARCHAR(255) DEFAULT NULL COMMENT '描述',
    `deleted_at`   BIGINT                 NULL COMMENT '删除时间',
    `is_del`       TINYINT(2)   DEFAULT 0 NULL COMMENT '是否删除',
    `created_at`   BIGINT                 NULL COMMENT '创建时间',
    `created_uid`  BIGINT                 NULL COMMENT '创建人',
    `updated_at`   BIGINT                 NULL COMMENT '更新时间',
    `updated_uid`  BIGINT                 NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
    COMMENT ='供应商账户'
;


CREATE TABLE `finance_info`
(
    `id`           BIGINT(20)           NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `deleted_at`   BIGINT               NULL COMMENT '删除时间',
    `is_del`       TINYINT(2) DEFAULT 0 NULL COMMENT '是否删除',
    `created_at`   BIGINT               NULL COMMENT '创建时间',
    `created_uid`  BIGINT               NULL COMMENT '创建人',
    `updated_at`   BIGINT               NULL COMMENT '更新时间',
    `updated_uid`  BIGINT               NULL COMMENT '更新人',
    `provider_id`  BIGINT(20)           NOT NULL COMMENT '供应商id',
    `invoice_type` INT(2)               NULL COMMENT '发票类型',
    `currency`     INT(2)               NOT NULL COMMENT '结算币别',
    `tax_type`     INT(2)               NOT NULL COMMENT '税分类',
    `tax_rate`     DECIMAL(11, 6)       NULL COMMENT '默认税率',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
    COMMENT ='财务信息'
;


CREATE TABLE `item_running`
(
    `id`               BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `type`             TINYINT(4)   NOT NULL DEFAULT '0' COMMENT '平台商品ID 0:小程序ID 1:会员店ID 2:美妆店ID 3:母婴店ID 4:自定义ID',
    `name`             VARCHAR(10)  NOT NULL DEFAULT '' COMMENT '平台商品ID名称',
    `item_id`          BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '商品ID',
    `platform_item_id` BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '平台商品ID',
    `standard_name`    VARCHAR(150) NOT NULL DEFAULT '' COMMENT '产品标准名',
    `selling_points`   VARCHAR(150) NOT NULL DEFAULT '' COMMENT '卖点文案',
    `created_at`       BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`      BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`       BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`      BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`           TINYINT(4)   NOT NULL DEFAULT '0' COMMENT '是否删除，不能做业务。这是软删除标记',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT ='运营信息表'
    ENGINE = InnoDB
;


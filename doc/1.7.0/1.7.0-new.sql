CREATE TABLE `handing_sheet`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `sheet_name`    varchar(100)  NOT NULL DEFAULT '' COMMENT '表格名称',
    `start_time`    bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间，时间戳，单位秒',
    `end_time`      bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间，时间戳，单位秒',
    `platform`      varchar(255)  NOT NULL DEFAULT '' COMMENT '所属平台，多个用逗号隔开（0-其他，1-淘宝，2-有赞...）',
    `label`         varchar(100)  NOT NULL DEFAULT '' COMMENT '标签',
    `attachment`    varchar(5000) NOT NULL DEFAULT '' COMMENT '附件',
    `activity_type` tinyint(4) NOT NULL COMMENT '类型',
    `state`         tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期）',
    `created_at`    bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`   bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`    bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`   bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `is_del`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0否1是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='盘货表';

CREATE TABLE `handing_sheet_staff`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `handing_sheet_id` bigint(20) NOT NULL COMMENT '盘货表ID',
    `user_id`          bigint(20) NOT NULL COMMENT '职员uid',
    `created_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `is_del`           tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0否1是',
    PRIMARY KEY (`id`),
    KEY                `idx_handing_sheet_id` (`handing_sheet_id`)
) ENGINE=InnoDB COMMENT='盘货表关联的职员';

CREATE TABLE `handing_sheet_activity_event`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `handing_sheet_id` bigint(20) NOT NULL COMMENT '盘货表ID',
    `reached_amount`   decimal(12, 2) NOT NULL DEFAULT '0.00' COMMENT '达到的金额',
    `reduced_amount`   decimal(12, 2) NOT NULL DEFAULT '0.00' COMMENT '减少的金额（达到x元减少y元）',
    `created_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `is_del`           tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0否1是',
    PRIMARY KEY (`id`),
    KEY                `idx_handing_sheet_id` (`handing_sheet_id`)
) ENGINE=InnoDB COMMENT='盘货表对应的活动力度';

CREATE TABLE `handing_sheet_item`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `handing_sheet_id`  bigint(20) NOT NULL COMMENT '盘货表ID',
    `item_no`           varchar(128)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `item_id`           bigint(20) NOT NULL DEFAULT '0' COMMENT '商品ID',
    `item_sku_no`       varchar(128)   NOT NULL DEFAULT '' COMMENT '商品编码',
    `item_sku_id`       bigint(20) NOT NULL DEFAULT '0' COMMENT '商品ID',
    `gift_description`  varchar(100)   NOT NULL DEFAULT '' COMMENT '赠品机制',
    `gift_code`         varchar(100)  NOT NULL DEFAULT '' COMMENT '赠品编码',
    `taobao_link`       varchar(1000)  NOT NULL DEFAULT '' COMMENT '淘宝链接',
    `douyin_link`       varchar(1000)  NOT NULL DEFAULT '' COMMENT '抖音链接',
    `mini_program_link` varchar(1000)  NOT NULL DEFAULT '' COMMENT '小程序链接',
    `remark`            varchar(255)   NOT NULL DEFAULT '' COMMENT '备注',
    `is_passed`         tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否审核通过',
    `active_price`      decimal(14, 2)                    NOT NULL DEFAULT '0.00' COMMENT '活动价',
    `created_at`        bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`       bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`        bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`       bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `is_del`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0否1是',
    PRIMARY KEY (`id`),
    KEY                 `idx_handing_sheet_id` (`handing_sheet_id`)
) ENGINE=InnoDB COMMENT='盘货表关联的商品';

CREATE TABLE `handing_sheet_activity_text`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `handing_sheet_id` bigint(20) NOT NULL COMMENT '盘货表ID',
    `activity_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
    `content`          text COMMENT '活动内容',
    `created_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`       bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`      bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `is_del`           tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0否1是',
    PRIMARY KEY (`id`),
    KEY                `idx_handing_sheet_id` (`handing_sheet_id`)
) ENGINE=InnoDB COMMENT='盘货表活动内容';

create
index idx_sku_code on new_goods(sku_code);
create
index idx_item_id on platform_item(item_id);